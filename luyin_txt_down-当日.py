import http.client
import json
import math
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Union, Any

import requests
from config import (
    LOGIN_CONFIG, DOWNLOAD_CONFIG, STAFF_DIRECTORY_MAP, 
    API_CONFIG, DEFAULT_HEADERS, get_staff_directory
)


class DialogueProcessor:
    """对话处理器"""

    @staticmethod
    def generate_dialogue_info(xs_names: str, kh_names: str, dialogue_start_time: str,
                               dialogue_end_time: str, dialogue_slice_content: str) -> Tuple[str, str]:
        """生成对话信息"""
        # 清理输入参数
        xs_names = (xs_names or "未知销售").strip()
        kh_names = (kh_names or "未知客户").strip()

        # 构建时间范围
        time_range = f"{dialogue_start_time}至{dialogue_end_time}".replace(':', '_')

        # 构建文件名
        filename = f"({xs_names}-接待-{kh_names}){time_range}.txt"

        return filename, dialogue_slice_content


class FileManager:
    """文件管理器"""

    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """清理文件名中的非法字符"""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        return filename.strip()

    @staticmethod
    def save_file(file_name: str, content: str, base_dir: Union[str, Path] = "output") -> str:
        """保存文件到指定目录"""
        try:
            # 确保目录存在
            dir_path = Path(base_dir)
            dir_path.mkdir(parents=True, exist_ok=True)

            # 清理文件名
            clean_filename = FileManager.sanitize_filename(file_name)

            # 构建完整文件路径，避免重名
            file_path = FileManager._get_unique_filepath(dir_path / clean_filename)

            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            print(f"✓ 文件已保存: {file_path}")
            return str(file_path)

        except Exception as e:
            print(f"✗ 保存文件失败: {e}")
            raise

    # @staticmethod
    # def download_audio(file_name: str, content: bytes, base_dir: Union[str, Path] = "audio_output") -> str:
    #     """下载并保存二进制音频文件到指定目录"""
    #     try:
    #         # 确保目录存在
    #         dir_path = Path(base_dir)
    #         dir_path.mkdir(parents=True, exist_ok=True)
    #
    #         # 清理文件名
    #         clean_filename = FileManager.sanitize_filename(file_name)
    #
    #         # 构建完整文件路径，避免重名
    #         file_path = FileManager._get_unique_filepath(dir_path / clean_filename)
    #
    #         # 以二进制模式写入文件
    #         with open(file_path, 'wb') as f:
    #             f.write(content)
    #
    #         print(f"✓ 录音已下载: {file_path}")
    #         return str(file_path)
    #
    #     except Exception as e:
    #         print(f"✗ 下载录音失败: {e}")
    #         raise

    @staticmethod
    def _get_unique_filepath(file_path: Path) -> Path:
        """获取唯一的文件路径，避免重名"""
        if not file_path.exists():
            return file_path

        stem = file_path.stem
        suffix = file_path.suffix
        parent = file_path.parent

        counter = 1
        while True:
            new_path = parent / f"{stem}_{counter}{suffix}"
            if not new_path.exists():
                return new_path
            counter += 1


class LoginClient:
    """登录客户端"""

    def __init__(self):
        self.verify_token = LOGIN_CONFIG["verify_token"]
        self.base_url = LOGIN_CONFIG["base_url"]
        self.session = requests.Session()
        self.dialogue_aggregation_list: List[Dict] = []
        self.login_token: Optional[str] = None
        self.image_id: Optional[str] = None
        self.img: Optional[str] = None

        # 设置默认请求头
        self.session.headers.update(DEFAULT_HEADERS)

    def get_verify_code(self) -> None:
        """获取验证码图片"""
        try:
            url = f"{self.base_url}/ci/api/auth/login/captcha2.jpg"
            response = self.session.get(url, timeout=API_CONFIG["timeout"])
            response.raise_for_status()

            data = response.json()
            if 'data' in data:
                self.image_id = data["data"]["imageId"]
                self.img = data["data"]["img"]
            else:
                raise RuntimeError("验证码响应格式错误")

        except Exception as e:
            raise RuntimeError(f"获取验证码失败: {e}")

    def verify(self) -> str:
        """解析验证码"""
        if not self.img:
            raise RuntimeError("验证码图片未获取")

        try:
            url = API_CONFIG["captcha_api_url"]
            data = {
                "token": self.verify_token,
                "type": API_CONFIG["captcha_type"],
                "image": str(self.img).replace("data:image/JPEG;base64,", ''),
            }
            headers = {"Content-Type": "application/json"}

            response = requests.post(url, headers=headers, json=data, timeout=API_CONFIG["timeout"])
            response.raise_for_status()

            result = response.json()
            if 'data' in result and 'data' in result['data']:
                return result['data']['data']
            else:
                raise RuntimeError("验证码解析响应格式错误")

        except Exception as e:
            raise RuntimeError(f"验证码解析失败: {e}")

    def login(self, username: str, password: str) -> None:
        """登录"""
        try:
            # 获取验证码
            self.get_verify_code()
            # 解析验证码
            code = self.verify()

            # 登录请求
            data = {
                "username": username,
                "password": password,
                "captchaCode": code,
                "imageId": self.image_id
            }

            response = self.session.post(
                f"{self.base_url}/ci/api/auth/login/account",
                json=data,
                timeout=API_CONFIG["timeout"]
            )
            response.raise_for_status()

            login_data = response.json()

            if login_data.get("statusCode") == 200 and 'data' in login_data:
                self.login_token = login_data['data']['accessToken']
                print("✓ 登录成功")
            else:
                raise RuntimeError(f"登录失败: {login_data}")

        except Exception as e:
            raise RuntimeError(f"登录过程失败: {e}")

    def get_today_range(self) -> Tuple[str, str]:
        """获取当天的时间范围"""
        now = datetime.now()

        start_of_today = datetime(now.year, now.month, now.day, 0, 0, 0)
        end_of_today = datetime(now.year, now.month, now.day, 23, 59, 59)

        start_str = start_of_today.strftime("%Y-%m-%d %H:%M:%S")
        end_str = end_of_today.strftime("%Y-%m-%d %H:%M:%S")

        return start_str, end_str

    def recordings_list(self, date_range: Optional[Tuple[str, str]] = None, staff_id_list: list = []) -> None:
        """获取录音列表"""
        if date_range is None:
            start_date, end_date = self.get_today_range()
        else:
            start_date, end_date = date_range

        print(f"正在获取录音列表: {start_date} 到 {end_date}")

        # 先获取总数
        data = {
            "dialogueStartTime": start_date,
            "dialogueEndTime": end_date,
            "pageNumber": 1,
            "pageSize": 1,
            "sortMode": "DESC"
        }
        if staff_id_list :
            data["staffCondition"]= {
                "staffIdList": staff_id_list,
                "departmentIdList": []
            }

        try:
            response = self._do_recordings_list(data)
            total = response["data"]["total"]
            print(f"共找到 {total} 条录音记录")

            if total == 0:
                print("⚠ 没有找到录音记录")
                return

            # 计算需要拉取的页数
            page_size = DOWNLOAD_CONFIG["page_size"]
            total_pages = math.ceil(total / page_size)

            for page in range(1, total_pages + 1):
                print(f"正在获取第 {page}/{total_pages} 页...")

                data.update({
                    "pageNumber": page,
                    "pageSize": page_size
                })

                response = self._do_recordings_list(data)
                self._handle_result(response)

                # 添加延迟避免请求过快
                time.sleep(DOWNLOAD_CONFIG["request_delay"])

            print(f"✓ 共获取到 {len(self.dialogue_aggregation_list)} 条对话记录")

        except Exception as e:
            print(f"✗ 获取录音列表失败: {e}")
            raise

    def _do_recordings_list(self, data: Dict) -> Dict:
        """执行录音列表请求"""
        headers = {
            'accesstoken': self.login_token,
            'content-type': 'application/json',
            'cache-control': 'no-cache',
            'origin': 'https://caip.mlamp.cn',
            'referer': 'https://caip.mlamp.cn/ci/app/conversation',
        }

        response = self.session.post(
            f"{self.base_url}/ci/api/analysis/session-result/dialogue/search",
            headers=headers,
            json=data,
            timeout=API_CONFIG["timeout"]
        )
        response.raise_for_status()
        return response.json()

    def _handle_result(self, response_data: Dict) -> None:
        """处理响应结果"""
        try:
            if response_data.get("statusCode") != 200:
                print(f"⚠ API返回错误状态: {response_data}")
                return

            data = response_data.get('data', {})
            dialogue_aggregation = data.get('dialogueAggregation', [])

            if not dialogue_aggregation:
                print("⚠ 未找到对话聚合数据")
                return

            # 处理对话聚合列表
            for aggregation in dialogue_aggregation:
                if isinstance(aggregation, dict):
                    dialogue_info_list = aggregation.get('dialogueInfoList', [])
                    if isinstance(dialogue_info_list, list):
                        self.dialogue_aggregation_list.extend(dialogue_info_list)

        except Exception as e:
            print(f"✗ 处理响应结果失败: {e}")

    # def download_dialogue_content(self, base_dir: Union[str, Path] = None) -> None:
    #     """下载录音文件"""
    #     if not self.dialogue_aggregation_list:
    #         print("⚠ 没有可下载的对话记录")
    #         return
    #
    #     print(f"开始下载 {len(self.dialogue_aggregation_list)} 条录音...")
    #
    #     # 创建时间戳目录
    #     if base_dir is None:
    #         timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    #         base_dir = Path('录音') / timestamp
    #
    #     headers = {
    #         'accesstoken': self.login_token,
    #         'content-type': 'application/json',
    #         'cache-control': 'no-cache',
    #         'origin': 'https://caip.mlamp.cn',
    #         'referer': 'https://caip.mlamp.cn/ci/app/conversation',
    #     }
    #
    #     success_count = 0
    #
    #     for i, item in enumerate(self.dialogue_aggregation_list, 1):
    #         try:
    #             print(f"正在下载第 {i}/{len(self.dialogue_aggregation_list)} 条录音...")
    #
    #             data = {
    #                 "dialogueStartTime": item.get('dialogueStartTime'),
    #                 "dialogueId": item.get('dialogueId'),
    #                 "eventId": []
    #             }
    #
    #             # 获取录音详情
    #             response = self.session.post(
    #                 f"{self.base_url}/ci/api/analysis/session-result/dialogue-detail/search",
    #                 headers=headers,
    #                 json=data,
    #                 timeout=30
    #             )
    #             response.raise_for_status()
    #
    #             dialogueId = item.get('dialogueId'),
    #
    #             # 组装录音数据
    #             audioIdList, dialogueStartTime, dialogueEndTime, tenantId, file_name = self._build_dialogue_content(
    #                 response.json())
    #
    #             _data = {
    #                 "audioIdList": audioIdList,
    #                 "dialogueStartTime": dialogueStartTime,
    #                 "dialogueEndTime": dialogueEndTime,
    #                 "tenantId": tenantId,
    #                 "dialogueId": dialogueId[0]
    #             }
    #
    #             _headers = {
    #                 'accept': 'application/json, text/plain, */*',
    #                 'accept-language': 'zh_CN',
    #                 'accesstoken': self.login_token,
    #                 'cache-control': 'no-cache',
    #                 'origin': 'https://caip.mlamp.cn',
    #                 'pragma': 'no-cache',
    #                 'priority': 'u=1, i',
    #                 'referer': 'https://caip.mlamp.cn/ci/app/conversation',
    #                 'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Microsoft Edge";v="134"',
    #                 'sec-ch-ua-mobile': '?0',
    #                 'sec-ch-ua-platform': '"Windows"',
    #                 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    #                 'content-type': 'application/json'
    #             }
    #
    #             # 下载录音文件
    #             response = requests.post(
    #                 f"{self.base_url}/ci/api/resource-manager/resource/audio/download",
    #                 headers=_headers,
    #                 json=_data,
    #                 timeout=1000
    #             )
    #             FileManager.download_audio(file_name, response.content, base_dir)
    #
    #             success_count += 1
    #             time.sleep(0.5)
    #
    #         except Exception as e:
    #             print(f"✗ 下载第 {i} 条录音失败: {e}")
    #             continue
    #
    #     print(f"✓ 录音下载完成! 成功: {success_count}, 失败: {len(self.dialogue_aggregation_list) - success_count}")

    def download_dialogue_txt(self, target_date: str = None) -> None:
        """下载录音文本"""
        if not self.dialogue_aggregation_list:
            print("⚠ 没有可下载的对话记录")
            return

        print(f"开始下载 {len(self.dialogue_aggregation_list)} 条对话记录...")

        # 使用指定日期或当天的日期
        if target_date is None:
            today = datetime.now()
            target_date = today.strftime("%Y-%m-%d")
        
        # 构建基础目录：/录音/YYYY-MM-DD/
        base_dir = Path(DOWNLOAD_CONFIG["base_dir"]) / target_date

        headers = {
            'accesstoken': self.login_token,
            'content-type': 'application/json',
            'cache-control': 'no-cache',
            'origin': 'https://caip.mlamp.cn',
            'referer': 'https://caip.mlamp.cn/ci/app/conversation',
        }

        success_count = 0

        for i, item in enumerate(self.dialogue_aggregation_list, 1):
            try:
                print(f"正在下载第 {i}/{len(self.dialogue_aggregation_list)} 条文本记录...")

                data = {
                    "dialogueStartTime": item.get('dialogueStartTime'),
                    "dialogueId": item.get('dialogueId'),
                    "eventId": []
                }

                response = self.session.post(
                    f"{self.base_url}/ci/api/analysis/session-result/dialogue-detail/search",
                    headers=headers,
                    json=data,
                    timeout=API_CONFIG["timeout"]
                )
                response.raise_for_status()

                # 组装文本数据
                file_name, content, staff_names = self._build_dialogue_text(response.json())
                
                # 根据员工姓名确定目录
                staff_directory = self._get_staff_directory_from_names(staff_names)
                final_dir = base_dir / staff_directory
                
                FileManager.save_file(file_name, content, final_dir)

                success_count += 1
                time.sleep(DOWNLOAD_CONFIG["download_delay"])

            except Exception as e:
                print(f"✗ 下载第 {i} 条文本记录失败: {e}")
                continue

        print(f"✓ 文本下载完成! 成功: {success_count}, 失败: {len(self.dialogue_aggregation_list) - success_count}")

    def _get_staff_directory_from_names(self, staff_names: List[str]) -> str:
        """根据员工姓名列表确定目录"""
        # 遍历所有员工姓名，找到第一个匹配的目录
        for staff_name in staff_names:
            directory = get_staff_directory(staff_name)
            if directory != "其他":
                return directory
        return "其他"

    def _build_dialogue_content(self, response_data: Dict) -> tuple[list[Any], str, str, str, str]:
        """构建录音上下文"""
        dialogue_item = response_data.get('data', {})
        dialogue_start_time = dialogue_item.get('dialogueStartTime', '')
        dialogue_end_time = dialogue_item.get('dialogueEndTime', '')
        audioIdList = dialogue_item.get('dialogueAudioIdList', [])
        tenant_id_origin = dialogue_item.get('upPageDialogueId', '')

        # 销售人员
        dialogue_active_staff_list = dialogue_item.get('dialogueActiveStaffList', [])
        xs_names = ','.join([item.get("name", "未知") for item in dialogue_active_staff_list])

        # 客户人员
        dialogue_active_contact_list = dialogue_item.get('dialogueActiveContactList', [])
        kh_names = ','.join([item.get("name", "未知") for item in dialogue_active_contact_list])

        # 拼接内容
        time_range = f"{dialogue_start_time}至{dialogue_end_time}".replace(':', '_')

        return audioIdList, dialogue_start_time, dialogue_end_time, tenant_id_origin.split('-')[
            0], f"({xs_names}-接待-{kh_names}){time_range}.mp3"

    def _build_dialogue_text(self, response_data: Dict) -> Tuple[str, str, List[str]]:
        """构建对话文本"""
        try:
            dialogue_item = response_data.get('data', {})

            # 销售人员
            dialogue_active_staff_list = dialogue_item.get('dialogueActiveStaffList', [])
            staff_names = [item.get("name", "未知") for item in dialogue_active_staff_list]
            xs_names = ','.join(staff_names)

            # 客户人员
            dialogue_active_contact_list = dialogue_item.get('dialogueActiveContactList', [])
            kh_names = ','.join([item.get("name", "未知") for item in dialogue_active_contact_list])

            # 录音开始结束时间
            dialogue_start_time = dialogue_item.get('dialogueStartTime', '')
            dialogue_end_time = dialogue_item.get('dialogueEndTime', '')

            # 拼接内容
            dialogue_slice_list = dialogue_item.get('dialogueSliceList', [])
            dialogue_slice_content = self._build_content(dialogue_slice_list)

            file_name, content = DialogueProcessor.generate_dialogue_info(
                xs_names, kh_names, dialogue_start_time, dialogue_end_time, dialogue_slice_content
            )
            
            return file_name, content, staff_names

        except Exception as e:
            print(f"✗ 构建对话文本失败: {e}")
            return f"error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt", f"构建对话文本失败: {e}", []

    def _build_content(self, dialogue_slice_list: List[Dict]) -> str:
        """构建对话内容"""
        if not isinstance(dialogue_slice_list, list):
            return "无效的对话数据格式\n"

        content_parts = []

        for item in dialogue_slice_list:
            try:
                if not isinstance(item, dict):
                    content_parts.append("无效的消息格式\n")
                    continue

                name = item.get('name', '未知用户').strip()
                audio_info = item.get('audioInfo', {})
                start_time = audio_info.get('startTime', '0')
                msg_content = item.get('msgContent', '').strip()

                # 格式化时间
                formatted_time = self._format_time(start_time)

                # 构建内容
                content_parts.append(f"{name} {formatted_time}")
                content_parts.append(msg_content)
                content_parts.append("")  # 添加空行分隔

            except Exception as e:
                print(f"✗ 构建单条内容时出错: {e}")
                content_parts.append(f"解析消息失败: {str(e)}\n")

        return "\n".join(content_parts)

    def _format_time(self, seconds_str: str) -> str:
        """将秒数字符串转换为 [HH:MM:SS] 格式"""
        try:
            total_seconds = int(float(seconds_str))
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            seconds = total_seconds % 60
            return f"[{hours:02d}:{minutes:02d}:{seconds:02d}]"
        except (ValueError, TypeError):
            return "[00:00:00]"

    def get_manage_user(self):
        """获取组织架构"""
        headers = {
            "Accesstoken": self.login_token
        }
        return self.session.get(f"{self.base_url}/ci/api/auth/dept/getManangeUser?hasUser=true",
                                headers=headers).json()


class MainApplication:
    """主应用程序"""

    def __init__(self):
        self.client = None
        self.username = LOGIN_CONFIG["username"]
        self.password = LOGIN_CONFIG["password"]

    def run(self):
        """运行主程序"""
        try:
            # 创建并登录客户端
            print("正在初始化...")
            self.client = LoginClient()

            print("开始登录...")
            self.client.login(self.username, self.password)

            # 使用当天的完整时间范围
            print("正在设置时间范围为当天整天...")
            date_range = self.client.get_today_range()
            print(f"时间范围: {date_range[0]} 到 {date_range[1]}")

            # 获取录音列表（所有员工）
            print("获取录音列表...")
            self.client.recordings_list(date_range=date_range, staff_id_list=[])

            if not self.client.dialogue_aggregation_list:
                print("没有找到录音记录，程序结束")
                return

            # 下载文本记录（目录会根据日期和员工自动分类）
            print("开始下载文本记录...")
            today = datetime.now()
            target_date = today.strftime("%Y-%m-%d")
            self.client.download_dialogue_txt(target_date)

            print("✓ 所有操作完成!")

        except Exception as e:
            print(f"✗ 程序执行失败: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    app = MainApplication()
    app.run()


if __name__ == "__main__":
    main()
