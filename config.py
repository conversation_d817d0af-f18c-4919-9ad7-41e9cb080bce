# 应用配置文件

# 登录配置
LOGIN_CONFIG = {
    "base_url": "https://caip.mlamp.cn",
    "username": "18561632488",
    "password": "qdait_740119",
    "verify_token": "7bddsoaRmpdmH0FC5zyG8yfmlZCU171jxzWZW2WPfhU"
}

# 下载配置
DOWNLOAD_CONFIG = {
    "base_dir": "录音",  # 固定基础目录
    "page_size": 30,     # 每页数据量
    "request_delay": 1,  # 请求延迟（秒）
    "download_delay": 0.5  # 下载延迟（秒）
}

# 人员目录映射配置
STAFF_DIRECTORY_MAP = {
    "科目二": ["杨大明", "王鑫", "张旭", "贾震"],
    "科目三": ["李长明", "王铁飞", "付豪", "李慎振"],
    "客服": ["王佳佳", "靳路萌"]
}

# API配置
API_CONFIG = {
    "captcha_api_url": "http://api.jfbym.com/api/YmServer/customApi",
    "captcha_type": 10110,
    "timeout": 30,
    "download_timeout": 1000
}

# 请求头配置
DEFAULT_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh_CN',
}

def get_staff_directory(staff_name: str) -> str:
    """根据员工姓名获取对应目录"""
    for directory, staff_list in STAFF_DIRECTORY_MAP.items():
        if staff_name in staff_list:
            return directory
    return "其他"  # 如果找不到对应目录，归类到"其他" 