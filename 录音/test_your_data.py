#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试您的实际数据 - 验证优化效果
使用您现有的录音数据和关键词配置
"""

import os
import sys
import re
import difflib
from typing import List, Dict, Tuple

# 简化版中文分词
def simple_chinese_tokenize(text: str) -> List[str]:
    """简化版中文分词"""
    words = []
    current_word = ""
    
    for char in text:
        if char.isspace() or char in '，。！？；：、':
            if current_word:
                words.append(current_word)
                current_word = ""
        else:
            current_word += char
    
    if current_word:
        words.append(current_word)
    
    # 进一步分割长词
    final_words = []
    for word in words:
        if len(word) > 4:
            for i in range(2, len(word)):
                if word[:i] in ['方向盘', '后视镜', '离合器', '刹车', '油门', '倒车', '入库', '观察', '控制']:
                    final_words.append(word[:i])
                    if word[i:]:
                        final_words.append(word[i:])
                    break
            else:
                final_words.append(word)
        else:
            final_words.append(word)
    
    return [w for w in final_words if len(w) >= 1]


class YourDataTester:
    def __init__(self):
        # 同义词词典
        self.synonyms = {
            '方向盘': ['方向', '转向盘', '舵'],
            '离合器': ['离合', '离合踏板'],
            '刹车': ['制动', '刹车踏板', '制动踏板'],
            '油门': ['加速踏板', '油门踏板'],
            '后视镜': ['反光镜', '倒车镜'],
            '倒车': ['后退', '倒退', '退车'],
            '入库': ['进库', '停车入位'],
            '观察': ['看', '注意', '观看', '查看'],
            '控制': ['掌控', '把控', '调节'],
            '车速': ['速度', '车的速度'],
            '修正': ['调整', '纠正', '改正'],
            '停车': ['停止', '停下', '刹停']
        }
        
        # 语音识别错误映射
        self.speech_errors = {
            '方向般': '方向盘', '方向盘盘': '方向盘',
            '离和': '离合', '离和器': '离合器',
            '刹者': '刹车', '刹茶': '刹车',
            '有门': '油门', '后事镜': '后视镜', '后是镜': '后视镜',
            '到车': '倒车', '倒者': '倒车',
            '入裤': '入库', '入苦': '入库',
            '观查': '观察', '观茶': '观察',
            '车数': '车速', '车宿': '车速',
            '修整': '修正', '调正': '调整'
        }

    def preprocess_text(self, text: str) -> str:
        """文本预处理"""
        # 移除时间戳和说话人标识
        text = re.sub(r'\[.*?\]', '', text)
        text = re.sub(r'^[^：]+：', '', text).strip()
        
        # 语音识别错误纠正
        for error, correct in self.speech_errors.items():
            text = text.replace(error, correct)
        
        # 移除无意义的重复字符
        text = re.sub(r'(.)\1{2,}', r'\1', text)
        
        # 移除语气词
        filler_words = ['嗯', '啊', '呃', '那个', '这个', '就是说', '然后']
        for filler in filler_words:
            text = text.replace(filler, ' ')
        
        text = re.sub(r'\s+', ' ', text).strip()
        return text

    def original_simple_match(self, keyword: str, text: str) -> bool:
        """原版本的简单匹配方法"""
        return keyword in text

    def enhanced_fuzzy_match(self, keyword: str, text: str) -> float:
        """增强的模糊匹配方法"""
        # 1. 精确匹配
        if keyword in text:
            return 1.0
        
        # 2. 同义词匹配
        if keyword in self.synonyms:
            for synonym in self.synonyms[keyword]:
                if synonym in text:
                    return 0.9
        
        # 3. 模糊匹配（编辑距离）
        words = simple_chinese_tokenize(text)
        best_score = 0.0
        
        for word in words:
            if len(word) >= 2:
                similarity = difflib.SequenceMatcher(None, keyword, word).ratio()
                if similarity > 0.75:
                    best_score = max(best_score, similarity * 0.8)
        
        # 4. 部分匹配
        if len(keyword) >= 3:
            for word in words:
                if keyword in word or word in keyword:
                    if abs(len(keyword) - len(word)) <= 2:
                        best_score = max(best_score, 0.7)
        
        return best_score

    def test_excel_keywords(self, excel_file_path: str):
        """测试Excel配置的关键词"""
        print(f"正在测试Excel文件: {excel_file_path}")

        try:
            # 简单检查文件是否存在
            if os.path.exists(excel_file_path):
                print(f"✅ 找到Excel文件: {excel_file_path}")
                print(f"📝 注意: 需要安装openpyxl或pandas来读取Excel内容")
                print(f"💡 建议: 可以将Excel内容复制到测试脚本中进行测试")
                return True
            else:
                print(f"❌ 文件不存在: {excel_file_path}")
                return False

        except Exception as e:
            print(f"❌ 检查文件失败: {e}")
            return False

    def test_sample_text(self, sample_text: str, keywords: List[str]):
        """测试示例文本"""
        print(f"\n=== 测试示例文本 ===")
        print(f"原始文本: {sample_text}")
        
        # 预处理
        processed_text = self.preprocess_text(sample_text)
        print(f"预处理后: {processed_text}")
        
        print(f"目标关键词: {keywords}")
        
        # 原版本匹配
        original_matches = 0
        original_matched = []
        for keyword in keywords:
            if self.original_simple_match(keyword, processed_text):
                original_matches += 1
                original_matched.append(keyword)
        
        original_accuracy = (original_matches / len(keywords)) * 100
        
        # 优化版本匹配
        enhanced_matches = 0
        enhanced_matched = []
        enhanced_details = []
        
        for keyword in keywords:
            score = self.enhanced_fuzzy_match(keyword, processed_text)
            if score > 0.6:
                enhanced_matches += 1
                enhanced_matched.append(keyword)
                enhanced_details.append(f"{keyword}({score:.2f})")
        
        enhanced_accuracy = (enhanced_matches / len(keywords)) * 100
        
        print(f"\n结果对比:")
        print(f"  原版本: {original_matches}/{len(keywords)} ({original_accuracy:.1f}%)")
        print(f"  匹配词: {original_matched}")
        print(f"  优化版: {enhanced_matches}/{len(keywords)} ({enhanced_accuracy:.1f}%)")
        print(f"  匹配词: {enhanced_details}")
        print(f"  提升: {enhanced_accuracy - original_accuracy:+.1f}%")
        
        return {
            'original_accuracy': original_accuracy,
            'enhanced_accuracy': enhanced_accuracy,
            'improvement': enhanced_accuracy - original_accuracy
        }


def main():
    """主函数"""
    print("=" * 60)
    print("测试您的实际数据 - 关键词匹配优化验证")
    print("=" * 60)
    
    tester = YourDataTester()
    
    # 第一步：查找Excel配置文件
    print("\n第一步：查找Excel配置文件")
    excel_files = []
    for file in os.listdir('.'):
        if file.endswith('.xlsx') and '关键词' in file:
            excel_files.append(file)
    
    if excel_files:
        print(f"找到Excel文件: {excel_files}")
        for excel_file in excel_files:
            tester.test_excel_keywords(excel_file)
    else:
        print("未找到关键词配置Excel文件，将使用示例数据测试")
    
    # 第二步：测试示例录音文本
    print(f"\n第二步：测试示例录音文本")
    
    # 示例1：语音识别错误
    sample1 = "张旭 [00:05:12] 现在你要观查后事镜，慢慢到车入裤，注意控制方向般。"
    keywords1 = ['观察', '后视镜', '倒车', '入库', '控制', '方向盘']
    result1 = tester.test_sample_text(sample1, keywords1)
    
    # 示例2：同义词表达
    sample2 = "张旭 [00:10:15] 你看一下反光镜，把车往后退，转向盘要慢慢打。"
    keywords2 = ['观察', '后视镜', '倒车', '方向盘']
    result2 = tester.test_sample_text(sample2, keywords2)
    
    # 示例3：复杂场景
    sample3 = "张旭 [00:15:20] 嗯，那个，现在练习倒车入库，首先观查后事镜，然后慢慢到车，车数要控制好。"
    keywords3 = ['倒车入库', '观察', '后视镜', '倒车', '车速', '控制']
    result3 = tester.test_sample_text(sample3, keywords3)
    
    # 总结
    print(f"\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    results = [result1, result2, result3]
    avg_original = sum(r['original_accuracy'] for r in results) / len(results)
    avg_enhanced = sum(r['enhanced_accuracy'] for r in results) / len(results)
    avg_improvement = sum(r['improvement'] for r in results) / len(results)
    
    print(f"测试样本数: {len(results)}")
    print(f"原版平均准确率: {avg_original:.1f}%")
    print(f"优化版平均准确率: {avg_enhanced:.1f}%")
    print(f"平均提升幅度: {avg_improvement:+.1f}%")
    
    print(f"\n下一步建议:")
    if avg_improvement > 10:
        print("✅ 优化效果显著，建议继续集成到实际系统中")
    elif avg_improvement > 0:
        print("⚠️ 有一定改善，建议调优参数后再测试")
    else:
        print("❌ 需要进一步分析和优化")
    
    print(f"\n如需测试更多数据，请:")
    print(f"1. 将您的录音文本文件放在当前目录")
    print(f"2. 修改此脚本中的测试用例")
    print(f"3. 运行: python3 test_your_data.py")


if __name__ == "__main__":
    main()
