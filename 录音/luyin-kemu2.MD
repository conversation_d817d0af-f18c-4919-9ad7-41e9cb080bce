# 录音文本分析系统 - 科目二专用版本 代码解析

## 文件概述
**文件名**: `luyin_kemu2-28日.py`  
**功能**: 扫描录音目录下的科目二目录，处理录音文件并上传到数据库  
**编码**: UTF-8  

---

## 1. 文件头部和导入模块

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
录音文本分析系统 - 科目二专用版本
功能：扫描录音目录下的科目二目录，处理录音文件并上传到数据库
"""

import os
import re
import pandas as pd
import pymysql
from openpyxl import Workbook
from datetime import datetime
import hashlib
from config import DB_CONFIG, FILE_CONFIG, KEYWORD_CONFIG, TEAM_CONFIG
```

**功能解析**:
- **Shebang**: 指定使用 Python3 解释器
- **编码声明**: 设置文件编码为 UTF-8，支持中文字符
- **文档字符串**: 描述程序的主要功能
- **模块导入**:
  - `os`: 操作系统接口，用于文件和目录操作
  - `re`: 正则表达式，用于文本模式匹配
  - `pandas`: 数据处理库，用于读取Excel文件
  - `pymysql`: MySQL数据库连接库
  - `openpyxl`: Excel文件操作库
  - `datetime`: 日期时间处理
  - `hashlib`: 哈希算法库，用于生成MD5值
  - `config`: 自定义配置模块，包含数据库、文件、关键词和团队配置

---

## 2. AudioTextAnalyzer 类定义和初始化

```python
class AudioTextAnalyzer:
    def __init__(self):
        # 当前脚本所在目录（录音目录）
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        # 工程根目录路径（向上1级）
        self.project_root = os.path.dirname(self.current_dir)
        
        self.subject = '科目二'  # 固定为科目二
        self.db_config = DB_CONFIG
        self.team_members = TEAM_CONFIG
        
        print(f"录音目录: {self.current_dir}")
        print(f"工程根目录: {self.project_root}")
        print(f"当前科目: {self.subject}")
```

**功能解析**:
- **类设计**: 采用面向对象编程，将录音文本分析功能封装在一个类中
- **路径设置**: 
  - `current_dir`: 获取当前脚本所在的录音目录
  - `project_root`: 向上一级获取工程根目录，用于查找关键词文件
- **属性初始化**:
  - `subject`: 固定设置为"科目二"，专门处理科目二相关录音
  - `db_config`: 从配置文件加载数据库连接参数
  - `team_members`: 从配置文件加载团队成员信息
- **调试输出**: 打印关键路径和配置信息，便于调试

---

## 3. 关键词文件查找方法

```python
def find_keyword_file(self):
    """根据科目二在工程根目录下查找对应的关键词文件"""
    for filename in os.listdir(self.project_root):
        if filename.startswith(self.subject) and filename.endswith('.xlsx'):
            keyword_file = os.path.join(self.project_root, filename)
            print(f"找到{self.subject}关键词文件: {filename}")
            return keyword_file
    
    print(f"错误: 未找到 {self.subject} 的关键词文件")
    return None
```

**功能解析**:
- **自动查找**: 在工程根目录下自动查找科目二关键词文件
- **匹配规则**: 文件名必须以"科目二"开头，并以".xlsx"结尾
- **返回值**: 成功时返回完整文件路径，失败时返回None
- **错误处理**: 如果找不到对应文件，输出错误信息
- **灵活性**: 不需要硬编码文件名，支持文件名变化

---

## 4. 关键词数据加载方法

```python
def load_keywords(self, keyword_file):
    """读取关键词Excel文件"""
    try:
        df = pd.read_excel(keyword_file, header=0)
        keywords_data = []
        
        for index, row in df.iterrows():
            if pd.isna(row.iloc[1]):
                continue

            keyword_group = str(row.iloc[1]).strip()
            strategy = str(row.iloc[2]).strip() if not pd.isna(row.iloc[2]) else ""
            score = int(row.iloc[3]) if not pd.isna(row.iloc[3]) else 0

            parsed_keywords = self.parse_keyword_group(keyword_group)

            keywords_data.append({
                'original': keyword_group,
                'parsed': parsed_keywords,
                'strategy': strategy,
                'score': score
            })

        print(f"成功加载 {len(keywords_data)} 个关键词组")
        return keywords_data

    except Exception as e:
        print(f"加载关键词文件失败: {e}")
        return []
```

**功能解析**:
- **Excel读取**: 使用pandas读取关键词Excel文件
- **数据结构**: 
  - 第2列(index=1): 关键词组
  - 第3列(index=2): 匹配策略
  - 第4列(index=3): 分值
- **数据清理**: 跳过空行，去除首尾空格
- **关键词解析**: 调用`parse_keyword_group`方法解析复杂关键词组
- **异常处理**: 捕获并打印加载失败的错误信息
- **返回格式**: 返回包含原始关键词、解析后关键词、策略和分值的字典列表

---

## 5. 科目二目录扫描方法

```python
def scan_kemu2_directories(self):
    """扫描当前录音目录下的所有科目二目录"""
    scan_results = []
    
    # 扫描录音时间目录（如 2025-07-23）
    for time_dir in os.listdir(self.current_dir):
        time_path = os.path.join(self.current_dir, time_dir)
        if not os.path.isdir(time_path):
            continue
            
        # 验证是否为日期格式
        if not re.match(r'\d{4}-\d{2}-\d{2}', time_dir):
            continue
            
        # 查找科目二目录
        subject_path = os.path.join(time_path, self.subject)
        if os.path.exists(subject_path) and os.path.isdir(subject_path):
            scan_results.append({
                'luyin_time': time_dir,
                'subject': self.subject,
                'path': subject_path
            })
            print(f"发现科目二目录: {time_dir}/{self.subject}")
    
    return scan_results
```

**功能解析**:
- **目录结构**: 扫描"录音时间/科目二"的目录结构
- **日期验证**: 使用正则表达式验证目录名是否为日期格式(YYYY-MM-DD)
- **目录过滤**: 只处理真实存在的目录，跳过文件
- **科目二查找**: 在每个日期目录下查找"科目二"子目录
- **结果收集**: 收集所有符合条件的科目二目录信息
- **返回数据**: 包含录音时间、科目和完整路径的字典列表

---

## 6. 文件名解析方法

```python
def parse_filename(self, filename):
    """解析录音文件名，提取人员姓名、所属队伍和录音时间"""
    try:
        pattern = r'\(([^-]+)-.*?\)(\d{4}-\d{2}-\d{2}) (\d{2}_\d{2}_\d{2})至(\d{4}-\d{2}-\d{2}) (\d{2}_\d{2}_\d{2})\.txt'
        match = re.match(pattern, filename)

        if match:
            username = match.group(1).strip()
            start_date = match.group(2)
            start_time = match.group(3)
            end_date = match.group(4)
            end_time = match.group(5)

            start_time_formatted = start_time.replace('_', ':')
            end_time_formatted = end_time.replace('_', ':')

            luyin_start_time = f"{start_date} {start_time_formatted}"
            luyin_end_time = f"{end_date} {end_time_formatted}"

            try:
                datetime.strptime(luyin_start_time, '%Y-%m-%d %H:%M:%S')
                datetime.strptime(luyin_end_time, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                print(f"警告: 时间格式解析错误 {filename}")
                luyin_start_time = None
                luyin_end_time = None

            # 根据姓名确定所属队伍
            group_name = ""
            for team, members in self.team_members.items():
                if username in members:
                    group_name = team
                    break

            if not group_name:
                print(f"警告: 未找到 {username} 对应的队伍")

            return username, group_name, luyin_start_time, luyin_end_time
        else:
            print(f"警告: 无法解析文件名格式: {filename}")
            return "", "", None, None

    except Exception as e:
        print(f"解析文件名失败 {filename}: {e}")
        return "", "", None, None
```

**功能解析**:
- **正则表达式**: 复杂的正则模式匹配文件名格式
- **文件名格式**: `(姓名-其他)日期 时间至日期 时间.txt`
- **信息提取**:
  - 用户姓名: 括号内破折号前的部分
  - 开始时间: 第一个日期和时间
  - 结束时间: 第二个日期和时间
- **时间格式化**: 将下划线转换为冒号，符合标准时间格式
- **时间验证**: 验证解析出的时间格式是否正确
- **队伍匹配**: 根据姓名查找对应的队伍信息
- **错误处理**: 处理各种解析失败的情况

---

## 7. 关键词组解析方法

```python
def parse_keyword_group(self, keyword_group):
    """解析关键词组"""
    sub_keywords = [kw.strip() for kw in keyword_group.split(',')]
    parsed = []
    
    for sub_kw in sub_keywords:
        if '|' in sub_kw:
            or_keywords = [k.strip() for k in sub_kw.split('|')]
            parsed.append(or_keywords)
        else:
            parsed.append([sub_kw.strip()])

    return parsed
```

**功能解析**:
- **关键词分组**: 按逗号分割主关键词组
- **OR逻辑**: 支持管道符(|)表示OR关系的关键词
- **数据结构**: 返回二维列表，每个子列表表示一组OR关键词
- **示例**: "关键词1,关键词2|关键词3" → [["关键词1"], ["关键词2", "关键词3"]]
- **灵活性**: 支持复杂的关键词组合逻辑

---

## 8. 句子分割方法

```python
def split_sentences(self, text_content):
    """按规则拆分句子，过滤客户对话"""
    lines = text_content.strip().split('\n')
    sentences = []
    i = 0

    while i < len(lines):
        current_line = lines[i].strip()

        # 跳过客户开头的对话
        if current_line.startswith('客户'):
            i += 1
            continue

        if re.search(r'\[.*?\]', current_line):
            if i + 1 < len(lines):
                next_line = lines[i + 1].strip()
                # 检查下一行是否也是客户对话
                if not next_line.startswith('客户'):
                    sentence = current_line + " " + next_line
                    sentences.append(sentence.strip())
                i += 2
            else:
                sentences.append(current_line)
                i += 1
        else:
            i += 1

    return sentences
```

**功能解析**:
- **客户对话过滤**: 跳过以"客户"开头的对话行
- **句子合并**: 将包含时间戳标记[...]的行与下一行合并
- **智能处理**: 避免将客户对话合并到教练对话中
- **文本清理**: 去除首尾空格，规范化文本格式
- **返回结果**: 返回过滤和合并后的句子列表

---

## 9. 关键词匹配方法

```python
def match_keywords(self, sentence, keyword_data):
    """匹配关键词并计算得分"""
    parsed_keywords = keyword_data['parsed']
    strategy = keyword_data['strategy']
    score = keyword_data['score']

    if not strategy:
        return self.full_match(sentence, parsed_keywords, score)
    else:
        return self.sequence_match(sentence, parsed_keywords, score)
```

**功能解析**:
- **策略分发**: 根据关键词数据中的策略选择匹配方法
- **全匹配**: 当策略为空时，使用全匹配模式
- **顺序匹配**: 当策略非空时，使用顺序匹配模式
- **得分返回**: 返回匹配成功的得分或0分

---

## 10. 全匹配方法

```python
def full_match(self, sentence, parsed_keywords, score):
    """全匹配：句子需包含所有子关键词"""
    for sub_keywords in parsed_keywords:
        found = False
        for keyword in sub_keywords:
            if keyword in sentence:
                found = True
                break
        if not found:
            return 0
    return score
```

**功能解析**:
- **AND逻辑**: 句子必须包含所有关键词组
- **OR逻辑**: 每个关键词组内的关键词是OR关系
- **匹配条件**: 每个关键词组至少匹配一个关键词
- **全部匹配**: 只有所有关键词组都匹配才返回得分

---

## 11. 顺序匹配方法

```python
def sequence_match(self, sentence, parsed_keywords, score):
    """顺序匹配：按关键词组顺序依次出现"""
    last_pos = -1

    for sub_keywords in parsed_keywords:
        min_pos = len(sentence)
        found = False

        for keyword in sub_keywords:
            pos = sentence.find(keyword, last_pos + 1)
            if pos != -1:
                min_pos = min(min_pos, pos)
                found = True

        if not found or min_pos <= last_pos:
            return 0

        last_pos = min_pos

    return score
```

**功能解析**:
- **顺序要求**: 关键词必须按照指定顺序在句子中出现
- **位置跟踪**: 使用`last_pos`跟踪上一个匹配位置
- **最早匹配**: 在关键词组中选择最早出现的关键词位置
- **严格顺序**: 后续关键词必须在前一个关键词之后出现

---

## 12. 录音文件分析方法

```python
def analyze_audio_file(self, file_path, keywords_data):
    """分析单个录音文件，支持关键词使用跟踪和得分限制"""
    try:
        with open(file_path, 'r', encoding=FILE_CONFIG['encoding']) as f:
            content = f.read()

        sentences = self.split_sentences(content)
        results = []
        total_score = 0
        used_keywords = set()  # 跟踪已使用的关键词

        for i, sentence in enumerate(sentences, 1):
            best_match = None
            best_score = 0

            for keyword_data in keywords_data:
                # 检查关键词是否已使用过
                if keyword_data['original'] in used_keywords:
                    continue
                    
                match_score = self.match_keywords(sentence, keyword_data)
                if match_score > best_score:
                    best_score = match_score
                    best_match = keyword_data

            hit_keyword_group = best_match['original'] if best_match else ""
            match_strategy = KEYWORD_CONFIG['sequence_match_strategy'] if best_match and best_match['strategy'] else KEYWORD_CONFIG['full_match_strategy']

            # 如果匹配到关键词，将其加入已使用集合
            if best_match and best_score > 0:
                used_keywords.add(best_match['original'])

            results.append({
                'sentence_order': i,
                'sentence_text': sentence,
                'hit_keyword_group': hit_keyword_group,
                'match_strategy': match_strategy,
                'score': best_score
            })

            total_score += best_score

        # 应用最高分限制
        max_score = KEYWORD_CONFIG['max_score_limit']
        if total_score > max_score:
            total_score = max_score

        return results, total_score, content

    except Exception as e:
        print(f"分析文件 {file_path} 失败: {e}")
        return [], 0, ""
```

**功能解析**:
- **文件读取**: 使用配置的编码读取录音文本文件
- **句子处理**: 调用句子分割方法处理文本内容
- **关键词去重**: 使用集合跟踪已使用的关键词，避免重复计分
- **最佳匹配**: 为每个句子寻找得分最高的关键词匹配
- **结果记录**: 记录句子顺序、文本、匹配关键词、策略和得分
- **得分限制**: 应用最高分限制，防止得分过高
- **异常处理**: 捕获文件读取和处理过程中的错误

---

## 13. 单个结果保存方法

```python
def save_single_result(self, result_dir, filename, results):
    """保存单个录音文件的分析结果到Excel"""
    try:
        wb = Workbook()
        ws = wb.active
        ws.title = "分析结果"

        headers = ['录音文件名', '句子顺序', '句子文本', '命中关键词组', '命中策略', '得分']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)

        for row, result in enumerate(results, 2):
            ws.cell(row=row, column=1, value=filename)
            ws.cell(row=row, column=2, value=result['sentence_order'])
            ws.cell(row=row, column=3, value=result['sentence_text'])
            ws.cell(row=row, column=4, value=result['hit_keyword_group'])
            ws.cell(row=row, column=5, value=result['match_strategy'])
            ws.cell(row=row, column=6, value=result['score'])

        result_file = os.path.join(result_dir, f"{os.path.splitext(filename)[0]}_分析结果.xlsx")
        wb.save(result_file)
        print(f"保存单个结果文件: {result_file}")

    except Exception as e:
        print(f"保存单个结果失败: {e}")
```

**功能解析**:
- **Excel创建**: 使用openpyxl创建新的Excel工作簿
- **表头设置**: 设置包含录音文件名、句子信息、匹配结果的表头
- **数据填充**: 逐行填充分析结果数据
- **文件命名**: 生成带"_分析结果"后缀的Excel文件名
- **异常处理**: 捕获Excel文件保存过程中的错误

---

## 14. 汇总结果保存方法

```python
def save_summary_result(self, result_dir, summary_data):
    """保存汇总结果到Excel"""
    try:
        wb = Workbook()
        ws = wb.active
        ws.title = "汇总结果"

        headers = ['人员姓名', '当日积分', '当日最高分录音文件名']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)

        for row, (filename, data) in enumerate(summary_data.items(), 2):
            ws.cell(row=row, column=1, value=data['username'])
            ws.cell(row=row, column=2, value=data['total_score'])
            ws.cell(row=row, column=3, value=filename)

        summary_file = os.path.join(result_dir, "汇总结果.xlsx")
        wb.save(summary_file)
        print(f"保存汇总结果文件: {summary_file}")

    except Exception as e:
        print(f"保存汇总结果失败: {e}")
```

**功能解析**:
- **汇总表格**: 创建人员当日积分汇总表
- **关键信息**: 包含人员姓名、当日积分和最高分录音文件名
- **固定文件名**: 保存为"汇总结果.xlsx"
- **数据源**: 基于去重后的summary_data生成汇总

---

## 15. 数据库保存方法

```python
def save_to_database(self, filename, content, results, total_score, username, group_name,
                     luyin_start_time, luyin_end_time, luyin_time):
    """保存数据到MySQL数据库 - VALUES语句批量插入版本"""
    conn = None
    try:
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()

        # 生成文件名的MD5哈希值
        username_md5 = hashlib.md5(filename.encode('utf-8')).hexdigest()

        # 检查记录是否已存在
        cursor.execute("SELECT id FROM audio_file WHERE username_md5 = %s", (username_md5,))
        existing_record = cursor.fetchone()

        if existing_record:
            print(f"数据库记录已存在，跳过: {filename} (MD5: {username_md5[:8]}...)")
            return False

        # 开始事务
        conn.begin()

        # 插入录音文件记录
        cursor.execute("""
            INSERT INTO audio_file (file_name, original_text, total_score, 
                                  group_name, username, luyin_start_time, luyin_end_time, 
                                  post, luyin_time, username_md5)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (filename, content, total_score, group_name, username,
              luyin_start_time, luyin_end_time, self.subject, luyin_time, username_md5))

        audio_file_id = cursor.lastrowid

        # 使用VALUES语句一次性插入所有记录
        if results:
            # 构建VALUES语句
            values_parts = []
            params = []

            for result in results:
                values_parts.append("(%s, %s, %s, %s, %s, %s)")
                params.extend([
                    audio_file_id,
                    result['sentence_order'],
                    result['sentence_text'],
                    result['hit_keyword_group'],
                    result['match_strategy'],
                    result['score']
                ])

            values_sql = ",".join(values_parts)

            sql = f"""
                INSERT INTO audio_sentence (audio_file_id, sentence_order, sentence_text, 
                                          hit_keyword_group, match_strategy, score)
                VALUES {values_sql}
            """

            # 一次性执行所有插入
            cursor.execute(sql, params)

        # 提交事务
        conn.commit()
        print(f"数据库保存成功: {filename} - 共保存 {len(results)} 条句子记录 (MD5: {username_md5[:8]}...)")
        return True

    except Exception as e:
        if conn:
            conn.rollback()
        print(f"数据库保存失败 {filename}: {e}")
        return False
    finally:
        if conn:
            cursor.close()
            conn.close()
```

**功能解析**:
- **重复检查**: 使用MD5哈希值检查文件是否已处理过
- **事务管理**: 使用数据库事务确保数据一致性
- **两表插入**: 分别插入audio_file主表和audio_sentence明细表
- **批量插入**: 使用VALUES语句一次性插入所有句子记录
- **外键关联**: 通过audio_file_id关联主表和明细表
- **异常处理**: 发生错误时回滚事务
- **连接管理**: 确保数据库连接正确关闭

---

## 16. 科目二目录处理方法

```python
def process_kemu2_directory(self, dir_info):
    """处理单个科目二目录"""
    print(f"\n=== 处理科目二目录 {dir_info['luyin_time']} ===")
    
    # 查找并加载关键词文件
    keyword_file = self.find_keyword_file()
    if not keyword_file:
        print(f"跳过目录 {dir_info['path']}: 未找到科目二关键词文件")
        return

    keywords_data = self.load_keywords(keyword_file)
    if not keywords_data:
        print(f"跳过目录 {dir_info['path']}: 关键词加载失败")
        return

    # 创建result目录
    result_dir = os.path.join(dir_info['path'], FILE_CONFIG['result_dir_name'])
    if not os.path.exists(result_dir):
        os.makedirs(result_dir)

    # 获取录音文件
    audio_files = []
    for f in os.listdir(dir_info['path']):
        if f.endswith(tuple(FILE_CONFIG['supported_extensions'])):
            audio_files.append(f)

    if not audio_files:
        print(f"未找到录音文件: {dir_info['path']}")
        return

    print(f"找到 {len(audio_files)} 个录音文件")
    summary_data = {}
    processed_count = 0
    skipped_count = 0

    # 处理每个录音文件
    for filename in audio_files:
        print(f"处理文件: {filename}")
        file_path = os.path.join(dir_info['path'], filename)

        username, group_name, luyin_start_time, luyin_end_time = self.parse_filename(filename)
        results, total_score, content = self.analyze_audio_file(file_path, keywords_data)

        if results:
            # 保存Excel结果
            self.save_single_result(result_dir, filename, results)

            # 保存到数据库（会自动检查重复）
            if self.save_to_database(filename, content, results, total_score,
                                   username, group_name, luyin_start_time, luyin_end_time,
                                   dir_info['luyin_time']):
                processed_count += 1
            else:
                skipped_count += 1

            # 记录汇总数据
            summary_data[filename] = {
                'username': username,
                'group_name': group_name,
                'luyin_start_time': luyin_start_time,
                'luyin_end_time': luyin_end_time,
                'total_score': total_score
            }

            print(f"完成: {filename}, 得分: {total_score}")
        else:
            print(f"处理失败: {filename}")

    # 同名人员去重：保留同名人员中得分最高的记录
    if summary_data:
        # 按人员姓名分组
        user_groups = {}
        for filename, data in summary_data.items():
            username = data['username']
            if username not in user_groups:
                user_groups[username] = []
            user_groups[username].append((filename, data))
        
        # 保留每个人员得分最高的记录
        filtered_summary = {}
        removed_files = []
        
        for username, files in user_groups.items():
            if len(files) > 1:
                # 按总得分排序，保留最高分的
                files.sort(key=lambda x: x[1]['total_score'], reverse=True)
                best_file = files[0]
                filtered_summary[best_file[0]] = best_file[1]
                
                # 记录被移除的文件
                for removed_file in files[1:]:
                    removed_files.append(removed_file[0])
                    print(f"移除低分记录: {removed_file[0]} (得分: {removed_file[1]['total_score']}) - 保留 {best_file[0]} (得分: {best_file[1]['total_score']})")
            else:
                # 只有一个文件，直接保留
                filtered_summary[files[0][0]] = files[0][1]
        
        # 保存过滤后的汇总结果
        self.save_summary_result(result_dir, filtered_summary)
        
        if removed_files:
            print(f"同名去重完成，移除了 {len(removed_files)} 个低分文件")

    print(f"科目二目录处理完成！新增: {processed_count}, 跳过: {skipped_count}")
```

**功能解析**:
- **完整流程**: 处理单个科目二目录的完整工作流程
- **前置检查**: 验证关键词文件和录音文件的存在
- **结果目录**: 自动创建result目录存储分析结果
- **文件过滤**: 根据支持的扩展名过滤录音文件
- **批量处理**: 逐个处理目录下的所有录音文件
- **同名去重**: 解决同一人员多个录音文件的重复问题
- **统计报告**: 输出处理成功和跳过的文件数量

---

## 17. 批量处理方法

```python
def process_all_kemu2(self):
    """处理所有科目二目录"""
    scan_results = self.scan_kemu2_directories()
    
    if not scan_results:
        print("未找到科目二目录")
        return

    print(f"\n找到 {len(scan_results)} 个科目二目录需要处理")

    for dir_info in scan_results:
        self.process_kemu2_directory(dir_info)

    print(f"\n=== 科目二批量处理完成 ===")
    print(f"处理的目录数: {len(scan_results)} 个")
```

**功能解析**:
- **批量扫描**: 扫描所有科目二目录
- **空目录处理**: 如果没有找到目录则提前退出
- **逐目录处理**: 依次处理每个找到的科目二目录
- **进度反馈**: 显示处理进度和最终统计

---

## 18. 主函数

```python
def main():
    """主函数"""
    print("=== 录音文本分析系统 - 科目二专用版本 ===")
    print("扫描录音目录下的所有科目二目录")
    print("支持重复检查，避免重复上传")
    print()

    analyzer = AudioTextAnalyzer()
    analyzer.process_all_kemu2()

    print("\n程序执行完成！")
    input("按回车键退出...")


if __name__ == "__main__":
    main()
```

**功能解析**:
- **程序入口**: 标准的Python程序入口点
- **欢迎信息**: 显示程序名称和主要功能描述
- **对象创建**: 创建AudioTextAnalyzer实例
- **执行处理**: 调用批量处理方法
- **交互结束**: 等待用户按回车键退出
- **脚本保护**: 使用`if __name__ == "__main__"`确保只在直接运行时执行

---

## 总体架构说明

### 数据流程
1. **扫描阶段**: 扫描录音目录，找到所有科目二子目录
2. **准备阶段**: 加载科目二关键词文件，准备匹配规则
3. **处理阶段**: 逐个处理录音文件，进行关键词匹配和得分计算
4. **存储阶段**: 将结果保存到Excel文件和MySQL数据库
5. **汇总阶段**: 生成人员积分汇总报告

### 关键特性
- **专用性**: 专门针对科目二录音处理，配置固化
- **智能匹配**: 支持全匹配和顺序匹配两种策略
- **重复避免**: 通过MD5哈希值避免重复处理同一文件
- **去重机制**: 同名人员保留最高分记录
- **异常健壮**: 全面的异常处理和错误报告
- **批量高效**: 支持批量处理多个目录和文件

### 依赖配置
- **DB_CONFIG**: 数据库连接配置
- **FILE_CONFIG**: 文件处理配置
- **KEYWORD_CONFIG**: 关键词匹配配置
- **TEAM_CONFIG**: 团队成员配置 