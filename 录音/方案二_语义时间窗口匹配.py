#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
录音文本分析系统 - 方案二：语义时间窗口匹配
功能：基于时间戳的接近程度进行跨句子关键词匹配，将时间相近的句子组合进行分析
"""

import os
import re
import pandas as pd
import pymysql
from openpyxl import Workbook
from datetime import datetime, timedelta
import hashlib
from config import DB_CONFIG, FILE_CONFIG, KEYWORD_CONFIG, TEAM_CONFIG


class AudioTextAnalyzerTimeWindow:
    def __init__(self, time_threshold=30):
        # 当前脚本所在目录（录音目录）
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        # 工程根目录路径（向上1级）
        self.project_root = os.path.dirname(self.current_dir)
        
        self.subject = '科目二'  # 固定为科目二
        self.db_config = DB_CONFIG
        self.team_members = TEAM_CONFIG
        self.time_threshold = time_threshold  # 时间阈值（秒）
        
        print(f"录音目录: {self.current_dir}")
        print(f"工程根目录: {self.project_root}")
        print(f"当前科目: {self.subject}")
        print(f"时间窗口阈值: {self.time_threshold}秒")

    def find_keyword_file(self):
        """根据科目二在工程根目录下查找对应的关键词文件"""
        for filename in os.listdir(self.project_root):
            if filename.startswith(self.subject) and filename.endswith('.xlsx'):
                keyword_file = os.path.join(self.project_root, filename)
                print(f"找到{self.subject}关键词文件: {filename}")
                return keyword_file
        
        print(f"错误: 未找到 {self.subject} 的关键词文件")
        return None

    def load_keywords(self, keyword_file):
        """读取关键词Excel文件"""
        try:
            df = pd.read_excel(keyword_file, header=0)
            keywords_data = []
            
            for index, row in df.iterrows():
                if pd.isna(row.iloc[1]):
                    continue

                keyword_group = str(row.iloc[1]).strip()
                strategy = str(row.iloc[2]).strip() if not pd.isna(row.iloc[2]) else ""
                score = int(row.iloc[3]) if not pd.isna(row.iloc[3]) else 0

                parsed_keywords = self.parse_keyword_group(keyword_group)

                keywords_data.append({
                    'original': keyword_group,
                    'parsed': parsed_keywords,
                    'strategy': strategy,
                    'score': score
                })

            print(f"成功加载 {len(keywords_data)} 个关键词组")
            return keywords_data

        except Exception as e:
            print(f"加载关键词文件失败: {e}")
            return []

    def scan_kemu2_directories(self):
        """扫描当前录音目录下的所有科目二目录"""
        scan_results = []
        
        # 扫描录音时间目录（如 2025-07-23）
        for time_dir in os.listdir(self.current_dir):
            time_path = os.path.join(self.current_dir, time_dir)
            if not os.path.isdir(time_path):
                continue
                
            # 验证是否为日期格式
            if not re.match(r'\d{4}-\d{2}-\d{2}', time_dir):
                continue
                
            # 查找科目二目录
            subject_path = os.path.join(time_path, self.subject)
            if os.path.exists(subject_path) and os.path.isdir(subject_path):
                scan_results.append({
                    'luyin_time': time_dir,
                    'subject': self.subject,
                    'path': subject_path
                })
                print(f"发现科目二目录: {time_dir}/{self.subject}")
        
        return scan_results

    def parse_filename(self, filename):
        """解析录音文件名，提取人员姓名、所属队伍和录音时间"""
        try:
            pattern = r'\(([^-]+)-.*?\)(\d{4}-\d{2}-\d{2}) (\d{2}_\d{2}_\d{2})至(\d{4}-\d{2}-\d{2}) (\d{2}_\d{2}_\d{2})\.txt'
            match = re.match(pattern, filename)

            if match:
                username = match.group(1).strip()
                start_date = match.group(2)
                start_time = match.group(3)
                end_date = match.group(4)
                end_time = match.group(5)

                start_time_formatted = start_time.replace('_', ':')
                end_time_formatted = end_time.replace('_', ':')

                luyin_start_time = f"{start_date} {start_time_formatted}"
                luyin_end_time = f"{end_date} {end_time_formatted}"

                try:
                    datetime.strptime(luyin_start_time, '%Y-%m-%d %H:%M:%S')
                    datetime.strptime(luyin_end_time, '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    print(f"警告: 时间格式解析错误 {filename}")
                    luyin_start_time = None
                    luyin_end_time = None

                # 根据姓名确定所属队伍
                group_name = ""
                for team, members in self.team_members.items():
                    if username in members:
                        group_name = team
                        break

                if not group_name:
                    print(f"警告: 未找到 {username} 对应的队伍")

                return username, group_name, luyin_start_time, luyin_end_time
            else:
                print(f"警告: 无法解析文件名格式: {filename}")
                return "", "", None, None

        except Exception as e:
            print(f"解析文件名失败 {filename}: {e}")
            return "", "", None, None

    def parse_keyword_group(self, keyword_group):
        """解析关键词组"""
        sub_keywords = [kw.strip() for kw in keyword_group.split(',')]
        parsed = []
        
        for sub_kw in sub_keywords:
            if '|' in sub_kw:
                or_keywords = [k.strip() for k in sub_kw.split('|')]
                parsed.append(or_keywords)
            else:
                parsed.append([sub_kw.strip()])

        return parsed

    def extract_timestamp(self, sentence):
        """从句子中提取时间戳"""
        try:
            # 匹配多种时间戳格式
            patterns = [
                r'\[(\d{2}:\d{2}:\d{2})\]',  # [14:23:45]
                r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]',  # [2024-07-28 14:23:45]
                r'(\d{2}:\d{2}:\d{2})',  # 14:23:45
            ]
            
            for pattern in patterns:
                match = re.search(pattern, sentence)
                if match:
                    time_str = match.group(1)
                    
                    # 尝试不同的时间格式解析
                    try:
                        if len(time_str) == 8:  # HH:MM:SS
                            # 假设是今天的时间
                            today = datetime.now().date()
                            time_obj = datetime.strptime(time_str, '%H:%M:%S').time()
                            return datetime.combine(today, time_obj)
                        else:  # YYYY-MM-DD HH:MM:SS
                            return datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        continue
            
            # 如果无法解析时间戳，返回None
            return None
            
        except Exception as e:
            print(f"时间戳解析失败: {sentence[:50]}... 错误: {e}")
            return None

    def split_sentences_enhanced(self, text_content):
        """改进的句子分割方法：每行为一句话，避免不合理合并"""
        lines = text_content.strip().split('\n')
        sentences = []
        
        for line in lines:
            line = line.strip()
            # 跳过空行
            if not line:
                continue
                
            # 跳过客户开头的对话
            if line.startswith('客户'):
                continue
                
            # 包含时间戳的行直接作为一句话
            if re.search(r'\[.*?\]', line):
                sentences.append(line)
            # 其他有内容的行也作为句子（如果需要的话）
            elif len(line) > 3:  # 避免过短的无意义行
                sentences.append(line)

        return sentences

    def get_time_window_sentences(self, sentences, sentence_times, current_index):
        """获取时间窗口内的所有句子"""
        current_time = sentence_times[current_index]
        
        if current_time is None:
            # 如果当前句子没有时间戳，只返回自身
            return {
                'window_sentences': [sentences[current_index]],
                'window_indices': [current_index],
                'time_range': "无时间戳",
                'sentences_used': 1
            }
        
        window_sentences = []
        window_indices = []
        
        # 查找时间窗口内的所有句子
        for i, other_time in enumerate(sentence_times):
            if other_time is None:
                continue
                
            time_diff = abs((current_time - other_time).total_seconds())
            if time_diff <= self.time_threshold:
                window_sentences.append(sentences[i])
                window_indices.append(i)
        
        # 按时间顺序排序
        if len(window_indices) > 1:
            paired = list(zip(window_indices, window_sentences, [sentence_times[i] for i in window_indices]))
            paired.sort(key=lambda x: x[2] if x[2] else datetime.min)
            window_indices, window_sentences, _ = zip(*paired)
            window_indices = list(window_indices)
            window_sentences = list(window_sentences)
        
        # 计算时间范围
        valid_times = [t for t in [sentence_times[i] for i in window_indices] if t is not None]
        if valid_times:
            min_time = min(valid_times)
            max_time = max(valid_times)
            time_range = f"{min_time.strftime('%H:%M:%S')}-{max_time.strftime('%H:%M:%S')}"
        else:
            time_range = "无有效时间戳"
        
        return {
            'window_sentences': window_sentences,
            'window_indices': window_indices,
            'time_range': time_range,
            'sentences_used': len(window_sentences)
        }

    def match_keywords(self, sentence, keyword_data):
        """匹配关键词并计算得分"""
        parsed_keywords = keyword_data['parsed']
        strategy = keyword_data['strategy']
        score = keyword_data['score']

        if not strategy:
            return self.full_match(sentence, parsed_keywords, score)
        else:
            return self.sequence_match(sentence, parsed_keywords, score)

    def full_match(self, sentence, parsed_keywords, score):
        """全匹配：句子需包含所有子关键词"""
        for sub_keywords in parsed_keywords:
            found = False
            for keyword in sub_keywords:
                if keyword in sentence:
                    found = True
                    break
            if not found:
                return 0
        return score

    def sequence_match(self, sentence, parsed_keywords, score):
        """顺序匹配：按关键词组顺序依次出现"""
        last_pos = -1

        for sub_keywords in parsed_keywords:
            min_pos = len(sentence)
            found = False

            for keyword in sub_keywords:
                pos = sentence.find(keyword, last_pos + 1)
                if pos != -1:
                    min_pos = min(min_pos, pos)
                    found = True

            if not found or min_pos <= last_pos:
                return 0

            last_pos = min_pos

        return score

    def analyze_audio_file_with_time_window(self, file_path, keywords_data):
        """带时间窗口的录音文件分析"""
        try:
            with open(file_path, 'r', encoding=FILE_CONFIG['encoding']) as f:
                content = f.read()

            sentences = self.split_sentences_enhanced(content)
            results = []
            total_score = 0
            used_keywords = set()  # 跟踪已使用的关键词
            
            # 提取每个句子的时间戳
            sentence_times = []
            for sentence in sentences:
                timestamp = self.extract_timestamp(sentence)
                sentence_times.append(timestamp)

            print(f"分析文件，共 {len(sentences)} 个句子，时间阈值: {self.time_threshold}秒")
            print(f"成功解析时间戳的句子: {sum(1 for t in sentence_times if t is not None)} 个")

            for i, sentence in enumerate(sentences):
                best_match = None
                best_score = 0
                time_window_info = None

                # 方法1：先尝试单句匹配
                for keyword_data in keywords_data:
                    if keyword_data['original'] in used_keywords:
                        continue
                        
                    match_score = self.match_keywords(sentence, keyword_data)
                    if match_score > best_score:
                        best_score = match_score
                        best_match = keyword_data

                # 方法2：如果单句匹配失败，尝试时间窗口匹配
                if best_score == 0:
                    time_window_info = self.get_time_window_sentences(sentences, sentence_times, i)
                    
                    # 合并时间窗口内的句子
                    window_text = " ".join(time_window_info['window_sentences'])
                    
                    for keyword_data in keywords_data:
                        if keyword_data['original'] in used_keywords:
                            continue
                            
                        # 在时间窗口内匹配
                        window_match_score = self.match_keywords(window_text, keyword_data)
                        if window_match_score > best_score:
                            best_score = window_match_score
                            best_match = keyword_data

                hit_keyword_group = best_match['original'] if best_match else ""
                match_strategy = KEYWORD_CONFIG['sequence_match_strategy'] if best_match and best_match['strategy'] else KEYWORD_CONFIG['full_match_strategy']
                
                # 确定匹配类型
                match_type = "单句匹配" if time_window_info is None else "时间窗口匹配"

                # 如果匹配到关键词，将其加入已使用集合
                if best_match and best_score > 0:
                    used_keywords.add(best_match['original'])

                # 构建结果
                result = {
                    'sentence_order': i + 1,
                    'sentence_text': sentence,
                    'hit_keyword_group': hit_keyword_group,
                    'match_strategy': match_strategy,
                    'match_type': match_type,
                    'score': best_score,
                    'timestamp': sentence_times[i].strftime('%H:%M:%S') if sentence_times[i] else "无时间戳"
                }

                if time_window_info:
                    result.update({
                        'time_window': time_window_info['time_range'],
                        'sentences_used': time_window_info['sentences_used'],
                        'window_indices': ','.join(map(str, [idx+1 for idx in time_window_info['window_indices']])),
                        'window_text': " ".join(time_window_info['window_sentences'])
                    })
                else:
                    result.update({
                        'time_window': sentence_times[i].strftime('%H:%M:%S') if sentence_times[i] else "无时间戳",
                        'sentences_used': 1,
                        'window_indices': str(i+1),
                        'window_text': sentence
                    })

                results.append(result)
                total_score += best_score

            # 应用最高分限制
            max_score = KEYWORD_CONFIG['max_score_limit']
            if total_score > max_score:
                total_score = max_score

            return results, total_score, content

        except Exception as e:
            print(f"分析文件 {file_path} 失败: {e}")
            return [], 0, ""

    def save_single_result(self, result_dir, filename, results):
        """保存单个录音文件的分析结果到Excel（时间窗口版）"""
        try:
            wb = Workbook()
            ws = wb.active
            ws.title = "分析结果"

            headers = ['录音文件名', '句子顺序', '句子文本', '时间戳', '命中关键词组', '命中策略', 
                      '匹配类型', '得分', '时间窗口', '使用句子数', '窗口句子序号', '窗口内容']
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            for row, result in enumerate(results, 2):
                ws.cell(row=row, column=1, value=filename)
                ws.cell(row=row, column=2, value=result['sentence_order'])
                ws.cell(row=row, column=3, value=result['sentence_text'])
                ws.cell(row=row, column=4, value=result['timestamp'])
                ws.cell(row=row, column=5, value=result['hit_keyword_group'])
                ws.cell(row=row, column=6, value=result['match_strategy'])
                ws.cell(row=row, column=7, value=result['match_type'])
                ws.cell(row=row, column=8, value=result['score'])
                ws.cell(row=row, column=9, value=result['time_window'])
                ws.cell(row=row, column=10, value=result['sentences_used'])
                ws.cell(row=row, column=11, value=result['window_indices'])
                ws.cell(row=row, column=12, value=result['window_text'])

            result_file = os.path.join(result_dir, f"{os.path.splitext(filename)[0]}_时间窗口分析结果.xlsx")
            wb.save(result_file)
            print(f"保存单个结果文件: {result_file}")

        except Exception as e:
            print(f"保存单个结果失败: {e}")

    def save_summary_result(self, result_dir, summary_data):
        """保存汇总结果到Excel"""
        try:
            wb = Workbook()
            ws = wb.active
            ws.title = "汇总结果"

            headers = ['人员姓名', '当日积分', '当日最高分录音文件名', '分析方法']
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            for row, (filename, data) in enumerate(summary_data.items(), 2):
                ws.cell(row=row, column=1, value=data['username'])
                ws.cell(row=row, column=2, value=data['total_score'])
                ws.cell(row=row, column=3, value=filename)
                ws.cell(row=row, column=4, value="时间窗口匹配")

            summary_file = os.path.join(result_dir, "汇总结果_时间窗口.xlsx")
            wb.save(summary_file)
            print(f"保存汇总结果文件: {summary_file}")

        except Exception as e:
            print(f"保存汇总结果失败: {e}")

    def process_kemu2_directory(self, dir_info):
        """处理单个科目二目录"""
        print(f"\n=== 处理科目二目录 {dir_info['luyin_time']} (时间窗口匹配) ===")
        
        # 查找并加载关键词文件
        keyword_file = self.find_keyword_file()
        if not keyword_file:
            print(f"跳过目录 {dir_info['path']}: 未找到科目二关键词文件")
            return

        keywords_data = self.load_keywords(keyword_file)
        if not keywords_data:
            print(f"跳过目录 {dir_info['path']}: 关键词加载失败")
            return

        # 创建result目录
        result_dir = os.path.join(dir_info['path'], FILE_CONFIG['result_dir_name'] + "_时间窗口")
        if not os.path.exists(result_dir):
            os.makedirs(result_dir)

        # 获取录音文件
        audio_files = []
        for f in os.listdir(dir_info['path']):
            if f.endswith(tuple(FILE_CONFIG['supported_extensions'])):
                audio_files.append(f)

        if not audio_files:
            print(f"未找到录音文件: {dir_info['path']}")
            return

        print(f"找到 {len(audio_files)} 个录音文件")
        summary_data = {}
        processed_count = 0
        skipped_count = 0

        # 处理每个录音文件
        for filename in audio_files:
            print(f"处理文件: {filename}")
            file_path = os.path.join(dir_info['path'], filename)

            username, group_name, luyin_start_time, luyin_end_time = self.parse_filename(filename)
            results, total_score, content = self.analyze_audio_file_with_time_window(file_path, keywords_data)

            if results:
                # 保存Excel结果
                self.save_single_result(result_dir, filename, results)

                # 记录汇总数据
                summary_data[filename] = {
                    'username': username,
                    'group_name': group_name,
                    'luyin_start_time': luyin_start_time,
                    'luyin_end_time': luyin_end_time,
                    'total_score': total_score
                }

                print(f"完成: {filename}, 得分: {total_score}")
            else:
                print(f"处理失败: {filename}")

        # 同名人员去重：保留同名人员中得分最高的记录
        if summary_data:
            # 按人员姓名分组
            user_groups = {}
            for filename, data in summary_data.items():
                username = data['username']
                if username not in user_groups:
                    user_groups[username] = []
                user_groups[username].append((filename, data))
            
            # 保留每个人员得分最高的记录
            filtered_summary = {}
            removed_files = []
            
            for username, files in user_groups.items():
                if len(files) > 1:
                    # 按总得分排序，保留最高分的
                    files.sort(key=lambda x: x[1]['total_score'], reverse=True)
                    best_file = files[0]
                    filtered_summary[best_file[0]] = best_file[1]
                    
                    # 记录被移除的文件
                    for removed_file in files[1:]:
                        removed_files.append(removed_file[0])
                        print(f"移除低分记录: {removed_file[0]} (得分: {removed_file[1]['total_score']}) - 保留 {best_file[0]} (得分: {best_file[1]['total_score']})")
                else:
                    # 只有一个文件，直接保留
                    filtered_summary[files[0][0]] = files[0][1]
            
            # 保存过滤后的汇总结果
            self.save_summary_result(result_dir, filtered_summary)
            
            if removed_files:
                print(f"同名去重完成，移除了 {len(removed_files)} 个低分文件")

        print(f"科目二目录处理完成！新增: {processed_count}, 跳过: {skipped_count}")

    def process_all_kemu2(self):
        """处理所有科目二目录"""
        scan_results = self.scan_kemu2_directories()
        
        if not scan_results:
            print("未找到科目二目录")
            return

        print(f"\n找到 {len(scan_results)} 个科目二目录需要处理")

        for dir_info in scan_results:
            self.process_kemu2_directory(dir_info)

        print(f"\n=== 科目二批量处理完成 (时间窗口匹配) ===")
        print(f"处理的目录数: {len(scan_results)} 个")


def main():
    """主函数"""
    print("=== 录音文本分析系统 - 方案二：语义时间窗口匹配 ===")
    print("基于时间戳的接近程度进行跨句子关键词匹配")
    print("将时间相近的句子组合进行智能分析")
    print()

    # 可以调整时间阈值
    time_threshold = int(input("请输入时间窗口阈值（秒，建议15-60，直接回车使用默认值30）: ") or "30")
    
    analyzer = AudioTextAnalyzerTimeWindow(time_threshold=time_threshold)
    analyzer.process_all_kemu2()

    print("\n程序执行完成！")
    input("按回车键退出...")


if __name__ == "__main__":
    main()