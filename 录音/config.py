#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库配置文件
请根据实际情况修改数据库连接参数 
"""

# MySQL数据库配置
DB_CONFIG = {
    'host': '*************',  # 数据库主机地址
    'user': 'root',  # 数据库用户名
    'password': 'Sg186186@123!',  # 数据库密码
    'database': 'luyin_analyse',  # 数据库名称
    'charset': 'utf8mb4',  # 字符集
    'port': 9506  # 数据库端口
}
TEAM_CONFIG = {
    "铁飞队": ["付豪", "李慎振", "王佳佳", "李长明", "王铁飞"],
    "张旭队": ["王鑫", "张旭", "靳路萌", "杨大明", "贾震"]
}

# 文件处理配置
FILE_CONFIG = {
    'supported_extensions': ['.txt'],  # 支持的文件扩展名
    'encoding': 'utf-8',  # 文件编码
    'result_dir_name': 'result'  # 结果目录名称
}

# 关键词匹配配置
KEYWORD_CONFIG = {
    'full_match_strategy': '全匹配',  # 全匹配策略名称
    'sequence_match_strategy': '顺序命中',  # 顺序匹配策略名称
    'max_score_limit': 41  # 最高分限制
}
