#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
录音文本分析系统 - 科目三专用版本 - 主题连续性检测匹配
功能：检测连续句子是否讨论同一主题，如果是，则作为一个整体进行关键词匹配
"""

import os
import re
import pandas as pd
import pymysql
from openpyxl import Workbook
from openpyxl.styles import PatternFill, Font, Alignment, Border, Side
from datetime import datetime
import hashlib
from config import DB_CONFIG, FILE_CONFIG, KEYWORD_CONFIG, TEAM_CONFIG


class AudioTextAnalyzerTopicContinuity:
    def __init__(self, similarity_threshold=0.3, max_group_size=5):
        # 当前脚本所在目录（录音目录）
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        # 工程根目录路径（向上1级）
        self.project_root = os.path.dirname(self.current_dir)
        
        self.subject = '科目三'  # 固定为科目三
        self.db_config = DB_CONFIG
        self.team_members = TEAM_CONFIG
        self.similarity_threshold = similarity_threshold  # 主题相似度阈值
        self.max_group_size = max_group_size  # 最大主题组大小
        
        # 科目三相关主题关键词库
        self.topic_keywords = {
            '起步操作': ['起步', '点火', '启动', '挂档', '松手刹', '踩离合', '油门'],
            '直线行驶': ['直线', '方向盘', '保持', '车道', '偏离', '修正', '稳定'],
            '变更车道': ['变道', '打灯', '观察', '并线', '超车', '让行', '安全距离'],
            '通过路口': ['路口', '红绿灯', '减速', '停车线', '观察', '让行', '转向'],
            '通过人行横道': ['人行横道', '斑马线', '行人', '减速', '停车', '礼让'],
            '通过学校区域': ['学校', '校园', '减速', '慢行', '注意', '学生'],
            '通过公交站台': ['公交站', '站台', '减速', '观察', '上下车', '让行'],
            '会车': ['会车', '对向', '来车', '让行', '减速', '保持距离'],
            '超车': ['超车', '变道', '加速', '观察', '安全', '返回'],
            '掉头': ['掉头', 'U型', '转向', '观察', '让行', '安全'],
            '加减档位': ['加档', '减档', '换档', '离合', '档位', '配合'],
            '靠边停车': ['靠边', '停车', '边线', '距离', '30公分', '拉手刹'],
            '夜间行驶': ['夜间', '灯光', '远光', '近光', '示廓灯', '危险灯'],
            '考试规则': ['考试', '规则', '扣分', '不合格', '评判', '标准'],
            '安全意识': ['安全', '观察', '注意', '危险', '避让', '防范'],
            '教学指导': ['教练', '学员', '指导', '纠正', '演示', '练习'],
            '车辆操作': ['方向盘', '离合', '刹车', '油门', '档位', '手刹']
        }
        
        # 新增：用于收集整体统计数据
        self.overall_statistics = {
            'processed_directories': [],
            'all_summary_data': {},  # 所有处理的文件汇总数据
            'date_statistics': {}    # 按日期的统计数据
        }
        
        print(f"录音目录: {self.current_dir}")
        print(f"工程根目录: {self.project_root}")
        print(f"当前科目: {self.subject}")
        print(f"主题相似度阈值: {self.similarity_threshold}")
        print(f"最大主题组大小: {self.max_group_size}")

    def find_keyword_file(self):
        """根据科目三在工程根目录下查找对应的关键词文件"""
        for filename in os.listdir(self.project_root):
            if filename.startswith(self.subject) and filename.endswith('.xlsx'):
                keyword_file = os.path.join(self.project_root, filename)
                print(f"找到{self.subject}关键词文件: {filename}")
                return keyword_file
        
        print(f"错误: 未找到 {self.subject} 的关键词文件")
        return None

    def load_keywords(self, keyword_file):
        """读取关键词Excel文件"""
        try:
            df = pd.read_excel(keyword_file, header=0)
            keywords_data = []
            
            for index, row in df.iterrows():
                if pd.isna(row.iloc[1]):
                    continue

                keyword_group = str(row.iloc[1]).strip()
                strategy = str(row.iloc[2]).strip() if not pd.isna(row.iloc[2]) else ""
                score = int(row.iloc[3]) if not pd.isna(row.iloc[3]) else 0

                parsed_keywords = self.parse_keyword_group(keyword_group)

                keywords_data.append({
                    'original': keyword_group,
                    'parsed': parsed_keywords,
                    'strategy': strategy,
                    'score': score
                })

            print(f"成功加载 {len(keywords_data)} 个关键词组")
            return keywords_data

        except Exception as e:
            print(f"加载关键词文件失败: {e}")
            return []

    def scan_kemu3_directories(self):
        """扫描当前录音目录下的所有科目三目录"""
        scan_results = []
        
        # 扫描录音时间目录（如 2025-07-23）
        for time_dir in os.listdir(self.current_dir):
            time_path = os.path.join(self.current_dir, time_dir)
            if not os.path.isdir(time_path):
                continue
                
            # 验证是否为日期格式
            if not re.match(r'\d{4}-\d{2}-\d{2}', time_dir):
                continue
                
            # 查找科目三目录
            subject_path = os.path.join(time_path, self.subject)
            if os.path.exists(subject_path) and os.path.isdir(subject_path):
                scan_results.append({
                    'luyin_time': time_dir,
                    'subject': self.subject,
                    'path': subject_path
                })
                print(f"发现科目三目录: {time_dir}/{self.subject}")
        
        return scan_results

    def parse_filename(self, filename):
        """解析录音文件名，提取人员姓名、所属队伍和录音时间"""
        try:
            pattern = r'\(([^-]+)-.*?\)(\d{4}-\d{2}-\d{2}) (\d{2}_\d{2}_\d{2})至(\d{4}-\d{2}-\d{2}) (\d{2}_\d{2}_\d{2})\.txt'
            match = re.match(pattern, filename)

            if match:
                username = match.group(1).strip()
                start_date = match.group(2)
                start_time = match.group(3)
                end_date = match.group(4)
                end_time = match.group(5)

                start_time_formatted = start_time.replace('_', ':')
                end_time_formatted = end_time.replace('_', ':')

                luyin_start_time = f"{start_date} {start_time_formatted}"
                luyin_end_time = f"{end_date} {end_time_formatted}"

                try:
                    datetime.strptime(luyin_start_time, '%Y-%m-%d %H:%M:%S')
                    datetime.strptime(luyin_end_time, '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    print(f"警告: 时间格式解析错误 {filename}")
                    luyin_start_time = None
                    luyin_end_time = None

                # 根据姓名确定所属队伍
                group_name = ""
                for team, members in self.team_members.items():
                    if username in members:
                        group_name = team
                        break

                if not group_name:
                    print(f"警告: 未找到 {username} 对应的队伍")

                return username, group_name, luyin_start_time, luyin_end_time
            else:
                print(f"警告: 无法解析文件名格式: {filename}")
                return "", "", None, None

        except Exception as e:
            print(f"解析文件名失败 {filename}: {e}")
            return "", "", None, None

    def parse_keyword_group(self, keyword_group):
        """解析关键词组"""
        sub_keywords = [kw.strip() for kw in keyword_group.split(',')]
        parsed = []
        
        for sub_kw in sub_keywords:
            if '|' in sub_kw:
                or_keywords = [k.strip() for k in sub_kw.split('|')]
                parsed.append(or_keywords)
            else:
                parsed.append([sub_kw.strip()])

        return parsed

    def split_sentences_enhanced(self, text_content):
        """改进的句子分割方法：每行为一句话，避免不合理合并"""
        lines = text_content.strip().split('\n')
        sentences = []
        
        for line in lines:
            line = line.strip()
            # 跳过空行
            if not line:
                continue
                
            # 跳过客户开头的对话
            if line.startswith('客户'):
                continue
                
            # 包含时间戳的行直接作为一句话
            if re.search(r'\[.*?\]', line):
                sentences.append(line)
            # 其他有内容的行也作为句子（如果需要的话）
            elif len(line) > 3:  # 避免过短的无意义行
                sentences.append(line)

        return sentences

    def identify_sentence_topic(self, sentence):
        """识别句子的主题"""
        topics_score = {}
        
        # 去除时间戳和说话人信息，只保留内容
        clean_sentence = re.sub(r'\[.*?\]', '', sentence)
        clean_sentence = re.sub(r'^[^：]+：', '', clean_sentence).strip()
        
        # 计算每个主题的相关度
        for topic, keywords in self.topic_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in clean_sentence:
                    score += 1
            
            # 归一化得分
            if keywords:
                topics_score[topic] = score / len(keywords)
        
        # 找到最高得分的主题
        if topics_score:
            best_topic = max(topics_score, key=topics_score.get)
            best_score = topics_score[best_topic]
            
            if best_score >= self.similarity_threshold:
                return best_topic, best_score
        
        return "通用对话", 0.0

    def calculate_topic_similarity(self, topic1, topic2):
        """计算两个主题之间的相似度"""
        if topic1 == topic2:
            return 1.0
        
        # 检查主题关键词的重叠度
        if topic1 in self.topic_keywords and topic2 in self.topic_keywords:
            keywords1 = set(self.topic_keywords[topic1])
            keywords2 = set(self.topic_keywords[topic2])
            
            intersection = keywords1.intersection(keywords2)
            union = keywords1.union(keywords2)
            
            if union:
                return len(intersection) / len(union)
        
        return 0.0

    def detect_topic_boundaries(self, sentences):
        """检测主题边界，将相似主题的句子分组"""
        if not sentences:
            return []
        
        # 为每个句子识别主题
        sentence_topics = []
        for sentence in sentences:
            topic, confidence = self.identify_sentence_topic(sentence)
            sentence_topics.append((topic, confidence))
        
        # 将相似主题的连续句子分组
        topic_groups = []
        current_group = [0]  # 从第一个句子开始
        current_topic = sentence_topics[0][0]
        
        for i in range(1, len(sentences)):
            current_sent_topic = sentence_topics[i][0]
            similarity = self.calculate_topic_similarity(current_topic, current_sent_topic)
            
            # 如果主题相似或是通用对话，继续添加到当前组
            if (similarity >= self.similarity_threshold or 
                current_sent_topic == "通用对话" or 
                current_topic == "通用对话"):
                
                # 检查组大小限制
                if len(current_group) < self.max_group_size:
                    current_group.append(i)
                    # 更新当前主题（选择置信度更高的）
                    if sentence_topics[i][1] > sentence_topics[current_group[0]][1]:
                        current_topic = current_sent_topic
                else:
                    # 组大小达到限制，开始新组
                    topic_groups.append({
                        'indices': current_group,
                        'topic': current_topic,
                        'sentences': [sentences[idx] for idx in current_group]
                    })
                    current_group = [i]
                    current_topic = current_sent_topic
            else:
                # 主题发生变化，结束当前组，开始新组
                topic_groups.append({
                    'indices': current_group,
                    'topic': current_topic,
                    'sentences': [sentences[idx] for idx in current_group]
                })
                current_group = [i]
                current_topic = current_sent_topic
        
        # 添加最后一组
        if current_group:
            topic_groups.append({
                'indices': current_group,
                'topic': current_topic,
                'sentences': [sentences[idx] for idx in current_group]
            })
        
        return topic_groups

    def match_keywords(self, sentence, keyword_data):
        """匹配关键词并计算得分"""
        parsed_keywords = keyword_data['parsed']
        strategy = keyword_data['strategy']
        score = keyword_data['score']

        if not strategy:
            return self.full_match(sentence, parsed_keywords, score)
        else:
            return self.sequence_match(sentence, parsed_keywords, score)

    def full_match(self, sentence, parsed_keywords, score):
        """全匹配：句子需包含所有子关键词"""
        for sub_keywords in parsed_keywords:
            found = False
            for keyword in sub_keywords:
                if keyword in sentence:
                    found = True
                    break
            if not found:
                return 0
        return score

    def sequence_match(self, sentence, parsed_keywords, score):
        """顺序匹配：按关键词组顺序依次出现"""
        last_pos = -1

        for sub_keywords in parsed_keywords:
            min_pos = len(sentence)
            found = False

            for keyword in sub_keywords:
                pos = sentence.find(keyword, last_pos + 1)
                if pos != -1:
                    min_pos = min(min_pos, pos)
                    found = True

            if not found or min_pos <= last_pos:
                return 0

            last_pos = min_pos

        return score

    def analyze_audio_file_with_topic_continuity(self, file_path, keywords_data):
        """带主题连续性检测的录音文件分析"""
        try:
            with open(file_path, 'r', encoding=FILE_CONFIG['encoding']) as f:
                content = f.read()

            sentences = self.split_sentences_enhanced(content)
            results = []
            total_score = 0
            used_keywords = set()  # 跟踪已使用的关键词
            
            # 检测主题边界
            topic_groups = self.detect_topic_boundaries(sentences)
            
            print(f"分析文件，共 {len(sentences)} 个句子，检测到 {len(topic_groups)} 个主题组")
            
            # 为每个主题组分析关键词
            for group_idx, group in enumerate(topic_groups):
                group_text = " ".join(group['sentences'])
                
                # 在主题组级别匹配关键词
                group_best_match = None
                group_best_score = 0
                
                for keyword_data in keywords_data:
                    if keyword_data['original'] in used_keywords:
                        continue
                        
                    match_score = self.match_keywords(group_text, keyword_data)
                    if match_score > group_best_score:
                        group_best_score = match_score
                        group_best_match = keyword_data
                
                # 为组内每个句子分配结果
                for sentence_idx in group['indices']:
                    sentence = sentences[sentence_idx]
                    
                    # 先尝试单句匹配
                    single_best_match = None
                    single_best_score = 0
                    
                    for keyword_data in keywords_data:
                        if keyword_data['original'] in used_keywords:
                            continue
                            
                        match_score = self.match_keywords(sentence, keyword_data)
                        if match_score > single_best_score:
                            single_best_score = match_score
                            single_best_match = keyword_data
                    
                    # 选择更好的匹配结果
                    if single_best_score > 0:
                        # 单句匹配成功，使用单句结果
                        best_match = single_best_match
                        best_score = single_best_score
                        match_type = "单句匹配"
                        match_context = sentence
                    elif group_best_score > 0:
                        # 单句匹配失败，使用主题组结果
                        best_match = group_best_match
                        best_score = group_best_score
                        match_type = "主题组匹配"
                        match_context = group_text
                    else:
                        # 都没匹配到
                        best_match = None
                        best_score = 0
                        match_type = "无匹配"
                        match_context = sentence
                    
                    hit_keyword_group = best_match['original'] if best_match else ""
                    match_strategy = KEYWORD_CONFIG['sequence_match_strategy'] if best_match and best_match['strategy'] else KEYWORD_CONFIG['full_match_strategy']
                    
                    # 如果匹配到关键词，将其加入已使用集合
                    if best_match and best_score > 0:
                        used_keywords.add(best_match['original'])
                    
                    results.append({
                        'sentence_order': sentence_idx + 1,
                        'sentence_text': sentence,
                        'hit_keyword_group': hit_keyword_group,
                        'match_strategy': match_strategy,
                        'match_type': match_type,
                        'score': best_score,
                        'topic_group': group_idx + 1,
                        'topic_name': group['topic'],
                        'group_size': len(group['indices']),
                        'group_indices': ','.join(map(str, [idx+1 for idx in group['indices']])),
                        'match_context': match_context
                    })
                    
                    total_score += best_score

            # 应用最高分限制
            max_score = KEYWORD_CONFIG['max_score_limit']
            if total_score > max_score:
                total_score = max_score

            return results, total_score, content

        except Exception as e:
            print(f"分析文件 {file_path} 失败: {e}")
            return [], 0, ""

    def save_single_result(self, result_dir, filename, results):
        """保存单个录音文件的分析结果到Excel（主题连续性版）"""
        try:
            wb = Workbook()
            ws = wb.active
            ws.title = "分析结果"

            headers = ['录音文件名', '句子顺序', '句子文本', '命中关键词组', '命中策略', 
                      '匹配类型', '得分', '主题组号', '主题名称', '组大小', '组句子序号', '匹配上下文']
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            for row, result in enumerate(results, 2):
                ws.cell(row=row, column=1, value=filename)
                ws.cell(row=row, column=2, value=result['sentence_order'])
                ws.cell(row=row, column=3, value=result['sentence_text'])
                ws.cell(row=row, column=4, value=result['hit_keyword_group'])
                ws.cell(row=row, column=5, value=result['match_strategy'])
                ws.cell(row=row, column=6, value=result['match_type'])
                ws.cell(row=row, column=7, value=result['score'])
                ws.cell(row=row, column=8, value=result['topic_group'])
                ws.cell(row=row, column=9, value=result['topic_name'])
                ws.cell(row=row, column=10, value=result['group_size'])
                ws.cell(row=row, column=11, value=result['group_indices'])
                ws.cell(row=row, column=12, value=result['match_context'])

            result_file = os.path.join(result_dir, f"{os.path.splitext(filename)[0]}_主题连续性分析结果.xlsx")
            wb.save(result_file)
            print(f"保存单个结果文件: {result_file}")

        except Exception as e:
            print(f"保存单个结果失败: {e}")

    def save_summary_result(self, result_dir, summary_data):
        """保存汇总结果到Excel"""
        try:
            wb = Workbook()
            ws = wb.active
            ws.title = "汇总结果"

            headers = ['人员姓名', '当日积分', '当日最高分录音文件名', '分析方法']
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            for row, (filename, data) in enumerate(summary_data.items(), 2):
                ws.cell(row=row, column=1, value=data['username'])
                ws.cell(row=row, column=2, value=data['total_score'])
                ws.cell(row=row, column=3, value=filename)
                ws.cell(row=row, column=4, value="主题连续性匹配")

            summary_file = os.path.join(result_dir, "汇总结果_主题连续性.xlsx")
            wb.save(summary_file)
            print(f"保存汇总结果文件: {summary_file}")

        except Exception as e:
            print(f"保存汇总结果失败: {e}")

    def save_to_database(self, filename, content, results, total_score, username, group_name,
                         luyin_start_time, luyin_end_time, luyin_time):
        """保存数据到MySQL数据库 - 主题连续性版本"""
        conn = None
        try:
            conn = pymysql.connect(**self.db_config)
            cursor = conn.cursor()

            # 生成文件名的MD5哈希值
            username_md5 = hashlib.md5(filename.encode('utf-8')).hexdigest()
            
            # 检查记录是否已存在
            cursor.execute("SELECT id FROM audio_file WHERE username_md5 = %s", (username_md5,))
            existing_record = cursor.fetchone()
            
            if existing_record:
                print(f"数据库记录已存在，跳过: {filename} (MD5: {username_md5[:8]}...)")
                return False

            # 开始事务
            conn.begin()

            # 插入录音文件记录
            cursor.execute("""
                INSERT INTO audio_file (file_name, original_text, total_score, 
                                      group_name, username, luyin_start_time, luyin_end_time, 
                                      post, luyin_time, username_md5, analysis_method)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (filename, content, total_score, group_name, username,
                  luyin_start_time, luyin_end_time, self.subject, luyin_time, username_md5, "主题连续性匹配"))

            audio_file_id = cursor.lastrowid

            # 批量插入句子分析记录（扩展版）
            if results:
                sentence_data = [
                    (
                        audio_file_id,
                        result['sentence_order'],
                        result['sentence_text'],
                        result['hit_keyword_group'],
                        result['match_strategy'],
                        result['score'],
                        result.get('match_type', ''),
                        result.get('topic_group', 0),
                        result.get('topic_name', ''),
                        result.get('group_size', 1),
                        result.get('group_indices', ''),
                        result.get('match_context', '')[:500]  # 限制长度
                    )
                    for result in results
                ]

                # 使用executemany进行批量插入（扩展字段）
                cursor.executemany("""
                    INSERT INTO audio_sentence (audio_file_id, sentence_order, sentence_text, 
                                              hit_keyword_group, match_strategy, score,
                                              match_type, topic_group, topic_name, group_size, 
                                              group_indices, match_context)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, sentence_data)

            # 提交事务
            conn.commit()
            print(f"数据库保存成功: {filename} (科目: {self.subject}, 录音时间: {luyin_time}) - 共保存 {len(results)} 条句子记录 (MD5: {username_md5[:8]}...)")
            return True

        except Exception as e:
            if conn:
                conn.rollback()
            print(f"数据库保存失败 {filename}: {e}")
            return False
        finally:
            if conn:
                cursor.close()
                conn.close()

    def process_kemu3_directory(self, dir_info):
        """处理单个科目三目录"""
        print(f"\n=== 处理科目三目录 {dir_info['luyin_time']} (主题连续性匹配) ===")
        
        # 查找并加载关键词文件
        keyword_file = self.find_keyword_file()
        if not keyword_file:
            print(f"跳过目录 {dir_info['path']}: 未找到科目三关键词文件")
            return

        keywords_data = self.load_keywords(keyword_file)
        if not keywords_data:
            print(f"跳过目录 {dir_info['path']}: 关键词加载失败")
            return

        # 创建result目录
        result_dir = os.path.join(dir_info['path'], FILE_CONFIG['result_dir_name'] + "_主题连续性")
        if not os.path.exists(result_dir):
            os.makedirs(result_dir)

        # 获取录音文件
        audio_files = []
        for f in os.listdir(dir_info['path']):
            if f.endswith(tuple(FILE_CONFIG['supported_extensions'])):
                audio_files.append(f)

        if not audio_files:
            print(f"未找到录音文件: {dir_info['path']}")
            return

        print(f"找到 {len(audio_files)} 个录音文件")
        summary_data = {}
        processed_count = 0
        skipped_count = 0

        # 处理每个录音文件
        for filename in audio_files:
            print(f"处理文件: {filename}")
            file_path = os.path.join(dir_info['path'], filename)

            username, group_name, luyin_start_time, luyin_end_time = self.parse_filename(filename)
            results, total_score, content = self.analyze_audio_file_with_topic_continuity(file_path, keywords_data)

            if results:
                # 保存Excel结果
                self.save_single_result(result_dir, filename, results)

                # 保存到数据库（会自动检查重复）
                if self.save_to_database(filename, content, results, total_score,
                                       username, group_name, luyin_start_time, luyin_end_time,
                                       dir_info['luyin_time']):
                    processed_count += 1
                else:
                    skipped_count += 1

                # 记录汇总数据
                summary_data[filename] = {
                    'username': username,
                    'group_name': group_name,
                    'luyin_start_time': luyin_start_time,
                    'luyin_end_time': luyin_end_time,
                    'total_score': total_score
                }

                print(f"完成: {filename}, 得分: {total_score}")
            else:
                print(f"处理失败: {filename}")

        # 同名人员去重：保留同名人员中得分最高的记录
        if summary_data:
            # 按人员姓名分组
            user_groups = {}
            for filename, data in summary_data.items():
                username = data['username']
                if username not in user_groups:
                    user_groups[username] = []
                user_groups[username].append((filename, data))
            
            # 保留每个人员得分最高的记录
            filtered_summary = {}
            removed_files = []
            
            for username, files in user_groups.items():
                if len(files) > 1:
                    # 按总得分排序，保留最高分的
                    files.sort(key=lambda x: x[1]['total_score'], reverse=True)
                    best_file = files[0]
                    filtered_summary[best_file[0]] = best_file[1]
                    
                    # 记录被移除的文件
                    for removed_file in files[1:]:
                        removed_files.append(removed_file[0])
                        print(f"移除低分记录: {removed_file[0]} (得分: {removed_file[1]['total_score']}) - 保留 {best_file[0]} (得分: {best_file[1]['total_score']})")
                else:
                    # 只有一个文件，直接保留
                    filtered_summary[files[0][0]] = files[0][1]
            
            # 保存过滤后的汇总结果
            self.save_summary_result(result_dir, filtered_summary)
            
            if removed_files:
                print(f"同名去重完成，移除了 {len(removed_files)} 个低分文件")

        print(f"科目三目录处理完成！新增: {processed_count}, 跳过: {skipped_count}")
        
        # 收集整体统计数据
        self.overall_statistics['processed_directories'].append(dir_info['luyin_time'])
        self.overall_statistics['date_statistics'][dir_info['luyin_time']] = {
            'processed_count': processed_count,
            'skipped_count': skipped_count,
            'total_files': len(audio_files),
            'summary_data': filtered_summary if summary_data else {}
        }
        
        # 将当前目录的汇总数据合并到总体数据中
        if summary_data:
            for filename, data in (filtered_summary if summary_data else {}).items():
                # 为每个文件数据添加日期信息和文件名
                data['luyin_date'] = dir_info['luyin_time']
                data['filename'] = filename
                self.overall_statistics['all_summary_data'][filename] = data

    def process_all_kemu3(self):
        """处理所有科目三目录"""
        scan_results = self.scan_kemu3_directories()
        
        if not scan_results:
            print("未找到科目三目录")
            return

        print(f"\n找到 {len(scan_results)} 个科目三目录需要处理")

        for dir_info in scan_results:
            self.process_kemu3_directory(dir_info)

        print(f"\n=== 科目三批量处理完成 (主题连续性匹配) ===")
        print(f"处理的目录数: {len(scan_results)} 个")
        
        # 生成整体汇总报告
        if self.overall_statistics['all_summary_data']:
            self.generate_overall_summary_report()
        
        # 生成所有科目的员工成绩汇总表（作为最后执行的脚本）
        self.generate_final_employee_summary_report()

    def generate_overall_summary_report(self):
        """生成整体汇总报告Excel文件"""
        try:
            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_filename = f"汇总报告_{timestamp}.xlsx"
            report_path = os.path.join(self.current_dir, report_filename)
            
            # 创建Excel工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "科目汇总报告"
            
            # 准备数据：按人员和日期组织
            self.prepare_summary_data_by_person_and_date(ws)
            
            # 保存文件
            wb.save(report_path)
            print(f"\n=== 整体汇总报告生成成功 ===")
            print(f"报告文件: {report_filename}")
            print(f"保存路径: {report_path}")
            
        except Exception as e:
            print(f"生成整体汇总报告失败: {e}")

    def prepare_summary_data_by_person_and_date(self, ws):
        """按人员和日期组织数据并写入Excel"""
        try:
            # 收集所有日期并排序
            all_dates = sorted(self.overall_statistics['processed_directories'])
            
            # 按人员组织数据
            person_data = {}
            for filename, data in self.overall_statistics['all_summary_data'].items():
                username = data['username']
                luyin_date = data['luyin_date']
                
                if username not in person_data:
                    person_data[username] = {
                        'group_name': data['group_name'],
                        'dates': {}
                    }
                
                # 每个人在每个日期可能有多个文件，保留最高分的
                if luyin_date not in person_data[username]['dates']:
                    person_data[username]['dates'][luyin_date] = data
                else:
                    if data['total_score'] > person_data[username]['dates'][luyin_date]['total_score']:
                        person_data[username]['dates'][luyin_date] = data
            
            # 计算每个人的平均分
            for username in person_data:
                scores = [person_data[username]['dates'][date]['total_score'] 
                         for date in person_data[username]['dates']]
                person_data[username]['average_score'] = sum(scores) / len(scores) if scores else 0
            
            # 按平均分排序
            sorted_persons = sorted(person_data.items(), 
                                  key=lambda x: x[1]['average_score'], 
                                  reverse=True)
            
            # 写入Excel
            self.write_summary_to_excel(ws, sorted_persons, all_dates)
            
        except Exception as e:
            print(f"准备汇总数据失败: {e}")

    def write_summary_to_excel(self, ws, sorted_persons, all_dates):
        """将汇总数据写入Excel并设置样式"""
        try:
            # 设置样式
            yellow_fill = PatternFill(start_color="FFFF99", end_color="FFFF99", fill_type="solid")
            header_font = Font(bold=True, size=12)
            border = Border(left=Side(style='thin'), right=Side(style='thin'), 
                          top=Side(style='thin'), bottom=Side(style='thin'))
            center_alignment = Alignment(horizontal='center', vertical='center')
            
            current_row = 1
            
            # 科目三标题行
            avg_score = sum([person[1]['average_score'] for person in sorted_persons]) / len(sorted_persons) if sorted_persons else 0
            ws.merge_cells(f'A{current_row}:B{current_row}')
            title_cell = ws[f'A{current_row}']
            title_cell.value = f"科目三 (主题连续性匹配)    平均分    {avg_score:.0f} 分"
            title_cell.fill = yellow_fill
            title_cell.font = header_font
            title_cell.alignment = center_alignment
            title_cell.border = border
            ws[f'B{current_row}'].border = border
            
            # 日期标题行
            col = 3  # C列开始
            for date in all_dates:
                # 转换日期格式 2025-07-23 -> 7月23日
                try:
                    date_obj = datetime.strptime(date, '%Y-%m-%d')
                    formatted_date = f"{date_obj.month}月{date_obj.day}日"
                except:
                    formatted_date = date
                
                date_cell = ws.cell(row=current_row, column=col)
                date_cell.value = formatted_date
                date_cell.fill = yellow_fill
                date_cell.font = header_font
                date_cell.alignment = center_alignment
                date_cell.border = border
                col += 1
            
            current_row += 1
            
            # 子标题行
            # 固定的两列：人员姓名、当日得分
            name_header = ws.cell(row=current_row, column=1)
            name_header.value = "人员姓名"
            name_header.fill = yellow_fill
            name_header.font = Font(bold=True, size=10)
            name_header.alignment = center_alignment
            name_header.border = border
            
            score_header = ws.cell(row=current_row, column=2)
            score_header.value = "当日得分"
            score_header.fill = yellow_fill
            score_header.font = Font(bold=True, size=10)
            score_header.alignment = center_alignment
            score_header.border = border
            
            # 每个日期一列：最佳表现录音文件
            col = 3
            for date in all_dates:
                file_header = ws.cell(row=current_row, column=col)
                file_header.value = "最佳表现录音文件"
                file_header.fill = yellow_fill
                file_header.font = Font(bold=True, size=10)
                file_header.alignment = center_alignment
                file_header.border = border
                col += 1
            
            current_row += 1
            
            # 数据行
            for username, data in sorted_persons:
                # 人员姓名
                name_cell = ws.cell(row=current_row, column=1)
                name_cell.value = username
                name_cell.border = border
                name_cell.alignment = center_alignment
                
                # 平均得分
                avg_cell = ws.cell(row=current_row, column=2)
                avg_cell.value = f"{data['average_score']:.0f}"
                avg_cell.border = border
                avg_cell.alignment = center_alignment
                
                # 各日期数据 - 修改为每个日期显示得分和文件名
                col = 3
                for date in all_dates:
                    if date in data['dates']:
                        date_data = data['dates'][date]
                        filename = date_data.get('filename', '')
                        
                        # 提取关键信息：得分 + 简化文件名
                        pattern = r'\(([^-]+)-接待-([^)]+)\)(\d{4}-\d{2}-\d{2})'
                        match = re.search(pattern, filename)
                        if match:
                            # 格式：得分 (姓名-接待-客户)日期
                            display_text = f"{date_data['total_score']} ({match.group(1)}-接待-{match.group(2)}){match.group(3)}"
                        else:
                            display_text = f"{date_data['total_score']} {filename[:20]}..."
                        
                        date_cell = ws.cell(row=current_row, column=col)
                        date_cell.value = display_text
                        date_cell.border = border
                        date_cell.alignment = Alignment(horizontal='left', vertical='center')
                    else:
                        # 如果该日期没有数据，留空
                        date_cell = ws.cell(row=current_row, column=col)
                        date_cell.value = ""
                        date_cell.border = border
                    
                    col += 1
                
                current_row += 1
            
            # 设置列宽
            ws.column_dimensions['A'].width = 12  # 人员姓名
            ws.column_dimensions['B'].width = 10  # 当日得分
            # 日期列宽度
            for i in range(3, 3 + len(all_dates)):
                col_letter = chr(64 + i) if i <= 26 else chr(64 + (i-1)//26) + chr(65 + (i-1)%26)
                ws.column_dimensions[col_letter].width = 30  # 最佳表现录音文件列
            
            print(f"成功写入科目三汇总数据: {len(sorted_persons)}人, {len(all_dates)}个日期")
             
        except Exception as e:
            print(f"写入Excel失败: {e}")

    def generate_final_employee_summary_report(self):
        """生成所有科目的员工成绩汇总表"""
        try:
            print(f"\n=== 开始生成所有科目员工成绩汇总表 ===")
            
            # 扫描所有科目的汇总结果文件
            all_subject_data = self.scan_all_subjects_summary()
            
            if not all_subject_data:
                print("未找到任何科目的汇总结果文件，跳过生成员工成绩汇总表")
                return
            
            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_filename = f"员工成绩汇总表_{timestamp}.xlsx"
            report_path = os.path.join(self.current_dir, report_filename)
            
            # 创建Excel工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "员工成绩汇总表"
            
            # 按科目生成报告
            self.write_all_subjects_performance_report(ws, all_subject_data)
            
            # 保存文件
            wb.save(report_path)
            print(f"\n=== 员工成绩汇总表生成成功 ===")
            print(f"报告文件: {report_filename}")
            print(f"保存路径: {report_path}")
            
        except Exception as e:
            print(f"生成员工成绩汇总表失败: {e}")
            import traceback
            traceback.print_exc()

    def scan_all_subjects_summary(self):
        """扫描所有科目的汇总结果文件"""
        all_data = {}
        
        # 扫描录音时间目录
        for time_dir in os.listdir(self.current_dir):
            time_path = os.path.join(self.current_dir, time_dir)
            if not os.path.isdir(time_path) or not re.match(r'\d{4}-\d{2}-\d{2}', time_dir):
                continue
            
            # 扫描科目目录
            for subject_dir in os.listdir(time_path):
                subject_path = os.path.join(time_path, subject_dir)
                if not os.path.isdir(subject_path):
                    continue
                
                # 查找主题连续性汇总结果文件
                summary_files = [
                    os.path.join(subject_path, 'result_主题连续性', '汇总结果_主题连续性.xlsx'),
                    os.path.join(subject_path, 'result', '汇总结果.xlsx')
                ]
                
                for summary_file in summary_files:
                    if os.path.exists(summary_file):
                        print(f"发现汇总文件: {time_dir}/{subject_dir}/{os.path.basename(os.path.dirname(summary_file))}/{os.path.basename(summary_file)}")
                        
                        # 读取汇总数据
                        subject_data = self.read_subject_summary_file(summary_file)
                        if subject_data:
                            if subject_dir not in all_data:
                                all_data[subject_dir] = {}
                            all_data[subject_dir][time_dir] = subject_data
                        break  # 找到一个就跳出
        
        return all_data

    def read_subject_summary_file(self, file_path):
        """读取单个科目的汇总结果文件"""
        try:
            df = pd.read_excel(file_path)
            
            data = []
            for _, row in df.iterrows():
                try:
                    record = {
                        'username': str(row.iloc[0]) if not pd.isna(row.iloc[0]) else '',
                        'total_score': float(row.iloc[1]) if not pd.isna(row.iloc[1]) else 0,
                        'filename': str(row.iloc[2]) if not pd.isna(row.iloc[2]) else '',
                        'group_name': '',  # 不再从文件读取队伍信息
                        'start_time': '',  # 简化处理，不读取时间信息
                        'end_time': '',    # 简化处理，不读取时间信息
                    }
                    data.append(record)
                except Exception as e:
                    print(f"警告: 跳过无效行: {e}")
                    continue
            
            print(f"读取汇总文件成功: {len(data)} 条记录")
            return data
            
        except Exception as e:
            print(f"读取汇总文件失败 {file_path}: {e}")
            return []

    def write_all_subjects_performance_report(self, ws, all_subject_data):
        """写入所有科目的员工表现报告"""
        try:
            current_row = 1
            
            # 科目显示顺序和样式配置
            subject_configs = {
                '科目二': {
                    'fill_color': 'ADD8E6',      # 浅蓝色
                    'light_color': 'D9E2F3'      # 更浅的蓝色
                },
                '科目三': {
                    'fill_color': 'FFFF99',      # 黄色  
                    'light_color': 'FFFFCC'      # 浅黄色
                },
                '客服': {
                    'fill_color': 'FFE4E1',      # 浅粉色
                    'light_color': 'FFF0F0'      # 更浅的粉色
                }
            }
            
            # 按科目处理
            for subject in ['科目二', '科目三', '客服']:
                if subject in all_subject_data:
                    print(f"正在生成 {subject} 报告...")
                    current_row = self._write_subject_performance_section(
                        ws, subject, all_subject_data[subject], 
                        subject_configs[subject], current_row
                    )
                    current_row += 2  # 科目间留空行
            
            print(f"成功写入所有科目员工表现数据")
            
        except Exception as e:
            print(f"写入员工表现数据失败: {e}")
            import traceback
            traceback.print_exc()

    def _write_subject_performance_section(self, ws, subject, subject_data, style_config, start_row):
        """写入单个科目的表现数据段"""
        current_row = start_row
        
        # 按队伍组织数据
        from collections import defaultdict
        dept_data = defaultdict(lambda: defaultdict(lambda: defaultdict(list)))
        all_dates = set()
        
        for date, records in subject_data.items():
            all_dates.add(date)
            for record in records:
                department = record['group_name'] if record['group_name'] else '未分组'
                staff_name = record['username']
                
                analysis = {
                    'score': record['total_score'],
                    'filename': record['filename'],
                    'start_time': record['start_time'],
                    'end_time': record['end_time']
                }
                
                dept_data[department][date][staff_name].append(analysis)
        
        all_dates = sorted(all_dates)
        
        # 设置样式
        main_fill = PatternFill(start_color=style_config['fill_color'], 
                               end_color=style_config['fill_color'], fill_type='solid')
        light_fill = PatternFill(start_color=style_config['light_color'], 
                                end_color=style_config['light_color'], fill_type='solid')
        header_font = Font(name='微软雅黑', size=14, bold=True)
        sub_header_font = Font(name='微软雅黑', size=11, bold=True)
        border = Border(left=Side(style='thin'), right=Side(style='thin'), 
                       top=Side(style='thin'), bottom=Side(style='thin'))
        center_alignment = Alignment(horizontal='center', vertical='center')
        
        # 计算科目平均分
        all_scores = []
        for dept_dates in dept_data.values():
            for date_data in dept_dates.values():
                for staff_data in date_data.values():
                    for recording in staff_data:
                        all_scores.append(recording['score'])
        
        subject_avg = round(sum(all_scores) / len(all_scores), 1) if all_scores else 0
        
        # 写入科目标题
        title_text = f"{subject} (主题连续性匹配)     平均分  {subject_avg}分"
        merge_end_col = min(8, len(all_dates) + 3)
        ws.merge_cells(f'A{current_row}:{chr(64 + merge_end_col)}{current_row}')
        cell = ws[f'A{current_row}']
        cell.value = title_text
        cell.font = header_font
        cell.fill = main_fill
        cell.alignment = center_alignment
        cell.border = border
        
        # 为合并的单元格添加边框
        for col in range(1, merge_end_col + 1):
            ws.cell(row=current_row, column=col).border = border
        current_row += 1
        
        # 为每个部门创建区段
        for department, dept_dates in dept_data.items():
            current_row = self._write_department_section_final(
                ws, department, dept_dates, all_dates, light_fill, 
                sub_header_font, border, center_alignment, current_row
            )
            current_row += 1  # 部门间留一行空行
        
        return current_row

    def _write_department_section_final(self, ws, department, dept_dates, all_dates, 
                                       light_fill, sub_header_font, border, center_alignment, start_row):
        """写入部门表现数据"""
        current_row = start_row
        
        # 计算部门平均分
        all_scores = []
        for date_data in dept_dates.values():
            for staff_data in date_data.values():
                for recording in staff_data:
                    all_scores.append(recording['score'])
        
        dept_avg = round(sum(all_scores) / len(all_scores), 1) if all_scores else 0
        
        # 写入表头
        headers = ['姓名'] + all_dates + ['平均得分', '最佳录音文件']
        
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=current_row, column=col)
            cell.value = header
            cell.font = sub_header_font
            cell.fill = light_fill
            cell.alignment = center_alignment
            cell.border = border
        current_row += 1
        
        # 获取该部门所有员工
        staff_names = set()
        for date_data in dept_dates.values():
            staff_names.update(date_data.keys())
        
        # 写入员工数据
        for row_idx, staff_name in enumerate(sorted(staff_names)):
            col = 1
            
            # 斑马线背景色
            bg_color = 'F0F8FF' if row_idx % 2 == 0 else 'FFFFFF'
            bg_fill = PatternFill(start_color=bg_color, end_color=bg_color, fill_type='solid')
            
            # 员工姓名
            cell = ws.cell(row=current_row, column=col)
            cell.value = staff_name
            cell.fill = bg_fill
            cell.border = border
            cell.alignment = center_alignment
            col += 1
            
            # 各日期得分和最佳录音
            daily_scores = []
            best_recording = None
            best_score = 0
            
            for date in all_dates:
                if date in dept_dates and staff_name in dept_dates[date]:
                    recordings = dept_dates[date][staff_name]
                    day_score = round(sum(r['score'] for r in recordings) / len(recordings), 1)
                    daily_scores.append(day_score)
                    
                    # 找最佳录音
                    for recording in recordings:
                        if recording['score'] > best_score:
                            best_score = recording['score']
                            best_recording = recording['filename']
                else:
                    daily_scores.append(0)
                
                cell = ws.cell(row=current_row, column=col)
                cell.value = daily_scores[-1] if daily_scores[-1] > 0 else "-"
                cell.fill = bg_fill
                cell.border = border
                cell.alignment = center_alignment
                col += 1
            
            # 平均得分
            avg_score = round(sum(daily_scores) / len(daily_scores), 1) if daily_scores else 0
            cell = ws.cell(row=current_row, column=col)
            cell.value = avg_score if avg_score > 0 else "-"
            cell.fill = bg_fill
            cell.border = border
            cell.alignment = center_alignment
            col += 1
            
            # 最佳录音文件
            cell = ws.cell(row=current_row, column=col)
            if best_recording:
                pattern = r'\(([^-]+)-接待-([^)]+)\)(\d{4}-\d{2}-\d{2})'
                match = re.search(pattern, best_recording)
                if match:
                    short_name = f"({match.group(1)}-接待-{match.group(2)}){match.group(3)}"
                else:
                    short_name = best_recording[:25] + "..." if len(best_recording) > 25 else best_recording
                cell.value = short_name
            else:
                cell.value = "-"
            cell.fill = bg_fill
            cell.border = border
            cell.alignment = Alignment(horizontal='left', vertical='center')
            
            current_row += 1
        
        # 设置列宽
        ws.column_dimensions['A'].width = 12
        for i, date in enumerate(all_dates, 2):
            if i <= 26:
                col_letter = chr(64 + i)
            else:
                col_letter = chr(64 + (i-1)//26) + chr(65 + (i-1)%26)
            ws.column_dimensions[col_letter].width = 12
        
        # 平均得分和最佳录音列
        avg_col_idx = len(all_dates) + 2
        best_col_idx = len(all_dates) + 3
        
        if avg_col_idx <= 26:
            ws.column_dimensions[chr(64 + avg_col_idx)].width = 12
        if best_col_idx <= 26:
            ws.column_dimensions[chr(64 + best_col_idx)].width = 35
        
        return current_row


def main():
    """主函数"""
    print("=== 录音文本分析系统 - 科目三专用版本 - 主题连续性检测匹配 ===")
    print("检测连续句子是否讨论同一主题，进行智能主题分组匹配")
    print("支持基于科目三教学内容的主题识别和连续性分析")
    print("支持重复检查，避免重复上传")
    print()

    # 可以调整参数
    try:
        similarity_threshold = float(input("请输入主题相似度阈值（0.1-1.0，建议0.3，直接回车使用默认值）: ") or "0.3")
        max_group_size = int(input("请输入最大主题组大小（建议3-8，直接回车使用默认值5）: ") or "5")
    except ValueError:
        print("输入格式错误，使用默认值")
        similarity_threshold = 0.3
        max_group_size = 5

    analyzer = AudioTextAnalyzerTopicContinuity(
        similarity_threshold=similarity_threshold, 
        max_group_size=max_group_size
    )
    analyzer.process_all_kemu3()

    print("\n程序执行完成！")
    input("按回车键退出...")


if __name__ == "__main__":
    main() 