#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
录音文本分析系统 - 科目二优化版本
在原版luyin_kemu2.py基础上集成关键词匹配优化功能
主要改进：语音识别错误纠正、同义词匹配、模糊匹配算法
"""

import os
import re
import difflib
from typing import List, Dict, Tuple

# 导入原版本的其他功能
try:
    from luyin_kemu2 import AudioTextAnalyzer as OriginalAnalyzer
    print("✅ 成功导入原版分析器")
except ImportError:
    print("⚠️ 无法导入原版分析器，将创建独立版本")
    OriginalAnalyzer = None


class OptimizedKeywordMatcher:
    """优化的关键词匹配器"""
    
    def __init__(self):
        # 同义词词典 - 驾校专业术语
        self.synonyms = {
            '方向盘': ['方向', '转向盘', '舵'],
            '离合器': ['离合', '离合踏板'],
            '刹车': ['制动', '刹车踏板', '制动踏板'],
            '油门': ['加速踏板', '油门踏板'],
            '后视镜': ['反光镜', '倒车镜'],
            '倒车': ['后退', '倒退', '退车'],
            '入库': ['进库', '停车入位'],
            '观察': ['看', '注意', '观看', '查看'],
            '控制': ['掌控', '把控', '调节'],
            '车速': ['速度', '车的速度'],
            '修正': ['调整', '纠正', '改正'],
            '停车': ['停止', '停下', '刹停'],
            '起步': ['启动', '开始'],
            '换挡': ['换档', '挂挡'],
            '转弯': ['拐弯', '转向'],
            '直线': ['直行', '直走']
        }
        
        # 语音识别错误映射表
        self.speech_errors = {
            '方向般': '方向盘', '方向盘盘': '方向盘',
            '离和': '离合', '离和器': '离合器',
            '刹者': '刹车', '刹茶': '刹车',
            '有门': '油门', '后事镜': '后视镜', '后是镜': '后视镜',
            '到车': '倒车', '倒者': '倒车',
            '入裤': '入库', '入苦': '入库',
            '观查': '观察', '观茶': '观察',
            '车数': '车速', '车宿': '车速',
            '修整': '修正', '调正': '调整',
            '起不': '起步', '换当': '换挡',
            '转完': '转弯', '直现': '直线'
        }
    
    def preprocess_text(self, text: str) -> str:
        """智能文本预处理"""
        # 移除时间戳和说话人标识
        text = re.sub(r'\[.*?\]', '', text)
        text = re.sub(r'^[^：]+：', '', text).strip()
        
        # 语音识别错误纠正
        for error, correct in self.speech_errors.items():
            text = text.replace(error, correct)
        
        # 移除无意义的重复字符
        text = re.sub(r'(.)\1{2,}', r'\1', text)
        
        # 移除语气词和填充词
        filler_words = ['嗯', '啊', '呃', '那个', '这个', '就是说', '然后', '拉倒吧']
        for filler in filler_words:
            text = text.replace(filler, ' ')
        
        # 标准化空格
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    def enhanced_keyword_match(self, keyword: str, text: str) -> float:
        """增强的关键词匹配算法"""
        # 1. 精确匹配
        if keyword in text:
            return 1.0
        
        # 2. 同义词匹配
        if keyword in self.synonyms:
            for synonym in self.synonyms[keyword]:
                if synonym in text:
                    return 0.9  # 同义词匹配置信度稍低
        
        # 3. 模糊匹配（基于编辑距离）
        words = text.split()
        best_score = 0.0
        
        for word in words:
            if len(word) >= 2:
                similarity = difflib.SequenceMatcher(None, keyword, word).ratio()
                if similarity > 0.75:  # 相似度阈值
                    best_score = max(best_score, similarity * 0.8)
        
        # 4. 部分匹配（包含关系）
        if len(keyword) >= 3:
            for word in words:
                if keyword in word or word in keyword:
                    if abs(len(keyword) - len(word)) <= 2:
                        best_score = max(best_score, 0.7)
        
        return best_score


class OptimizedAudioTextAnalyzer:
    """优化版录音文本分析器"""
    
    def __init__(self):
        # 初始化优化匹配器
        self.matcher = OptimizedKeywordMatcher()
        
        # 如果原版分析器可用，继承其功能
        if OriginalAnalyzer:
            self.original = OriginalAnalyzer()
            print("✅ 集成原版分析器功能")
        else:
            self.original = None
            print("⚠️ 独立运行模式")
    
    def parse_keywords(self, keywords_str):
        """解析关键词字符串（保持与原版兼容）"""
        if not keywords_str:
            return []
        
        groups = keywords_str.split('|')
        parsed = []
        
        for group in groups:
            sub_keywords = [kw.strip() for kw in group.split('+') if kw.strip()]
            if sub_keywords:
                parsed.append(sub_keywords)
        
        return parsed
    
    def optimized_full_match(self, sentence, parsed_keywords, score):
        """优化的全匹配算法"""
        processed_sentence = self.matcher.preprocess_text(sentence)
        
        matched_groups = 0
        total_confidence = 0.0
        
        for sub_keywords in parsed_keywords:
            group_best_score = 0.0
            
            for keyword in sub_keywords:
                match_score = self.matcher.enhanced_keyword_match(keyword, processed_sentence)
                if match_score > 0.6:  # 匹配阈值
                    group_best_score = max(group_best_score, match_score)
            
            if group_best_score > 0.6:
                matched_groups += 1
                total_confidence += group_best_score
        
        # 计算最终得分
        if matched_groups == len(parsed_keywords):
            # 全部匹配成功
            avg_confidence = total_confidence / len(parsed_keywords)
            return int(score * avg_confidence)
        elif matched_groups >= len(parsed_keywords) * 0.8:
            # 部分匹配（80%以上）
            avg_confidence = total_confidence / len(parsed_keywords)
            return int(score * avg_confidence * 0.8)
        else:
            # 匹配失败
            return 0
    
    def optimized_sequence_match(self, sentence, parsed_keywords, score):
        """优化的顺序匹配算法"""
        processed_sentence = self.matcher.preprocess_text(sentence)
        last_pos = -1
        
        matched_groups = 0
        total_confidence = 0.0
        
        for sub_keywords in parsed_keywords:
            group_best_score = 0.0
            group_best_pos = len(processed_sentence)
            found = False
            
            for keyword in sub_keywords:
                # 寻找关键词在文本中的位置
                words = processed_sentence.split()
                for word in words:
                    word_pos = processed_sentence.find(word, last_pos + 1)
                    if word_pos != -1:
                        match_score = self.matcher.enhanced_keyword_match(keyword, word)
                        if match_score > 0.6 and word_pos > last_pos:
                            if match_score > group_best_score:
                                group_best_score = match_score
                                group_best_pos = word_pos
                                found = True
            
            if found and group_best_pos > last_pos:
                matched_groups += 1
                total_confidence += group_best_score
                last_pos = group_best_pos
            else:
                break  # 顺序匹配失败
        
        # 计算最终得分
        if matched_groups == len(parsed_keywords):
            avg_confidence = total_confidence / len(parsed_keywords)
            return int(score * avg_confidence)
        else:
            return 0
    
    def match_keywords(self, sentence, keyword_data):
        """优化的关键词匹配主函数（替换原版本）"""
        if isinstance(keyword_data, dict):
            # 新格式：字典包含parsed, strategy, score
            parsed_keywords = keyword_data['parsed']
            strategy = keyword_data['strategy']
            score = keyword_data['score']
        else:
            # 兼容旧格式
            parsed_keywords = self.parse_keywords(keyword_data)
            strategy = False
            score = 10
        
        if not strategy:
            return self.optimized_full_match(sentence, parsed_keywords, score)
        else:
            return self.optimized_sequence_match(sentence, parsed_keywords, score)
    
    def analyze_text_with_optimization(self, text: str, keywords_config: List[Dict]) -> Dict:
        """使用优化算法分析文本"""
        results = {
            'total_score': 0,
            'matched_keywords': [],
            'details': [],
            'processed_text': self.matcher.preprocess_text(text)
        }
        
        for keyword_config in keywords_config:
            score = self.match_keywords(text, keyword_config)
            
            if score > 0:
                results['total_score'] += score
                results['matched_keywords'].append({
                    'keywords': keyword_config.get('keywords', ''),
                    'score': score,
                    'confidence': score / keyword_config.get('score', 10)
                })
            
            results['details'].append({
                'keywords': keyword_config.get('keywords', ''),
                'expected_score': keyword_config.get('score', 10),
                'actual_score': score,
                'matched': score > 0
            })
        
        return results


def test_optimization():
    """测试优化效果"""
    print("=" * 60)
    print("优化版录音文本分析器测试")
    print("=" * 60)
    
    analyzer = OptimizedAudioTextAnalyzer()
    
    # 测试用例
    test_cases = [
        {
            'text': '张旭 [00:05:12] 现在你要观查后事镜，慢慢到车入裤，注意控制方向般。',
            'keywords_config': [
                {'keywords': '观察+后视镜', 'parsed': [['观察', '后视镜']], 'strategy': False, 'score': 10},
                {'keywords': '倒车+入库', 'parsed': [['倒车', '入库']], 'strategy': False, 'score': 15},
                {'keywords': '控制+方向盘', 'parsed': [['控制', '方向盘']], 'strategy': False, 'score': 10}
            ]
        },
        {
            'text': '张旭 [00:10:15] 你看一下反光镜，把车往后退，转向盘要慢慢打。',
            'keywords_config': [
                {'keywords': '观察+后视镜', 'parsed': [['观察', '后视镜']], 'strategy': False, 'score': 10},
                {'keywords': '倒车', 'parsed': [['倒车']], 'strategy': False, 'score': 10},
                {'keywords': '方向盘', 'parsed': [['方向盘']], 'strategy': False, 'score': 10}
            ]
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"原始文本: {test_case['text']}")
        
        result = analyzer.analyze_text_with_optimization(
            test_case['text'], 
            test_case['keywords_config']
        )
        
        print(f"预处理后: {result['processed_text']}")
        print(f"总得分: {result['total_score']}")
        print(f"匹配详情:")
        
        for detail in result['details']:
            status = "✅" if detail['matched'] else "❌"
            print(f"  {status} {detail['keywords']}: {detail['actual_score']}/{detail['expected_score']}")
    
    print(f"\n✅ 优化版本测试完成")
    print(f"💡 可以替换原版luyin_kemu2.py中的match_keywords方法")


if __name__ == "__main__":
    test_optimization()
