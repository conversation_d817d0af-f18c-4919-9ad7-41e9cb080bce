#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
录音文本分析系统 - 科目三专用版本
功能：扫描录音目录下的科目三目录，处理录音文件并上传到数据库
"""

import os
import re
import pandas as pd
import pymysql
from openpyxl import Workbook
from datetime import datetime
import hashlib
from config import DB_CONFIG, FILE_CONFIG, KEYWORD_CONFIG, TEAM_CONFIG


class AudioTextAnalyzer:
    def __init__(self):
        # 当前脚本所在目录（录音目录）
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        # 工程根目录路径（向上1级）
        self.project_root = os.path.dirname(self.current_dir)
        
        self.subject = '科目三'  # 固定为科目三
        self.db_config = DB_CONFIG
        self.team_members = TEAM_CONFIG
        
        print(f"录音目录: {self.current_dir}")
        print(f"工程根目录: {self.project_root}")
        print(f"当前科目: {self.subject}")

    def find_keyword_file(self):
        """根据科目三在工程根目录下查找对应的关键词文件"""
        for filename in os.listdir(self.project_root):
            if filename.startswith(self.subject) and filename.endswith('.xlsx'):
                keyword_file = os.path.join(self.project_root, filename)
                print(f"找到{self.subject}关键词文件: {filename}")
                return keyword_file
        
        print(f"错误: 未找到 {self.subject} 的关键词文件")
        return None

    def load_keywords(self, keyword_file):
        """读取关键词Excel文件"""
        try:
            df = pd.read_excel(keyword_file, header=0)
            keywords_data = []
            
            for index, row in df.iterrows():
                if pd.isna(row.iloc[1]):
                    continue

                keyword_group = str(row.iloc[1]).strip()
                strategy = str(row.iloc[2]).strip() if not pd.isna(row.iloc[2]) else ""
                score = int(row.iloc[3]) if not pd.isna(row.iloc[3]) else 0

                parsed_keywords = self.parse_keyword_group(keyword_group)

                keywords_data.append({
                    'original': keyword_group,
                    'parsed': parsed_keywords,
                    'strategy': strategy,
                    'score': score
                })

            print(f"成功加载 {len(keywords_data)} 个关键词组")
            return keywords_data

        except Exception as e:
            print(f"加载关键词文件失败: {e}")
            return []

    def scan_kemu3_directories(self):
        """扫描当前录音目录下的所有科目三目录"""
        scan_results = []
        
        # 扫描录音时间目录（如 2025-07-23）
        for time_dir in os.listdir(self.current_dir):
            time_path = os.path.join(self.current_dir, time_dir)
            if not os.path.isdir(time_path):
                continue
                
            # 验证是否为日期格式
            if not re.match(r'\d{4}-\d{2}-\d{2}', time_dir):
                continue
                
            # 查找科目三目录
            subject_path = os.path.join(time_path, self.subject)
            if os.path.exists(subject_path) and os.path.isdir(subject_path):
                scan_results.append({
                    'luyin_time': time_dir,
                    'subject': self.subject,
                    'path': subject_path
                })
                print(f"发现科目三目录: {time_dir}/{self.subject}")
        
        return scan_results

    def parse_filename(self, filename):
        """解析录音文件名，提取人员姓名、所属队伍和录音时间"""
        try:
            pattern = r'\(([^-]+)-.*?\)(\d{4}-\d{2}-\d{2}) (\d{2}_\d{2}_\d{2})至(\d{4}-\d{2}-\d{2}) (\d{2}_\d{2}_\d{2})\.txt'
            match = re.match(pattern, filename)

            if match:
                username = match.group(1).strip()
                start_date = match.group(2)
                start_time = match.group(3)
                end_date = match.group(4)
                end_time = match.group(5)

                start_time_formatted = start_time.replace('_', ':')
                end_time_formatted = end_time.replace('_', ':')

                luyin_start_time = f"{start_date} {start_time_formatted}"
                luyin_end_time = f"{end_date} {end_time_formatted}"

                try:
                    datetime.strptime(luyin_start_time, '%Y-%m-%d %H:%M:%S')
                    datetime.strptime(luyin_end_time, '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    print(f"警告: 时间格式解析错误 {filename}")
                    luyin_start_time = None
                    luyin_end_time = None

                # 根据姓名确定所属队伍
                group_name = ""
                for team, members in self.team_members.items():
                    if username in members:
                        group_name = team
                        break

                if not group_name:
                    print(f"警告: 未找到 {username} 对应的队伍")

                return username, group_name, luyin_start_time, luyin_end_time
            else:
                print(f"警告: 无法解析文件名格式: {filename}")
                return "", "", None, None

        except Exception as e:
            print(f"解析文件名失败 {filename}: {e}")
            return "", "", None, None

    def parse_keyword_group(self, keyword_group):
        """解析关键词组"""
        sub_keywords = [kw.strip() for kw in keyword_group.split(',')]
        parsed = []
        
        for sub_kw in sub_keywords:
            if '|' in sub_kw:
                or_keywords = [k.strip() for k in sub_kw.split('|')]
                parsed.append(or_keywords)
            else:
                parsed.append([sub_kw.strip()])

        return parsed

    def split_sentences(self, text_content):
        """按规则拆分句子，过滤客户对话"""
        lines = text_content.strip().split('\n')
        sentences = []
        i = 0

        while i < len(lines):
            current_line = lines[i].strip()

            # 跳过客户开头的对话
            if current_line.startswith('客户'):
                i += 1
                continue

            if re.search(r'\[.*?\]', current_line):
                if i + 1 < len(lines):
                    next_line = lines[i + 1].strip()
                    # 检查下一行是否也是客户对话
                    if not next_line.startswith('客户'):
                        sentence = current_line + " " + next_line
                        sentences.append(sentence.strip())
                    i += 2
                else:
                    sentences.append(current_line)
                    i += 1
            else:
                i += 1

        return sentences

    def match_keywords(self, sentence, keyword_data):
        """匹配关键词并计算得分"""
        parsed_keywords = keyword_data['parsed']
        strategy = keyword_data['strategy']
        score = keyword_data['score']

        if not strategy:
            return self.full_match(sentence, parsed_keywords, score)
        else:
            return self.sequence_match(sentence, parsed_keywords, score)

    def full_match(self, sentence, parsed_keywords, score):
        """全匹配：句子需包含所有子关键词"""
        for sub_keywords in parsed_keywords:
            found = False
            for keyword in sub_keywords:
                if keyword in sentence:
                    found = True
                    break
            if not found:
                return 0
        return score

    def sequence_match(self, sentence, parsed_keywords, score):
        """顺序匹配：按关键词组顺序依次出现"""
        last_pos = -1

        for sub_keywords in parsed_keywords:
            min_pos = len(sentence)
            found = False

            for keyword in sub_keywords:
                pos = sentence.find(keyword, last_pos + 1)
                if pos != -1:
                    min_pos = min(min_pos, pos)
                    found = True

            if not found or min_pos <= last_pos:
                return 0

            last_pos = min_pos

        return score

    def analyze_audio_file(self, file_path, keywords_data):
        """分析单个录音文件，支持关键词使用跟踪和得分限制"""
        try:
            with open(file_path, 'r', encoding=FILE_CONFIG['encoding']) as f:
                content = f.read()

            sentences = self.split_sentences(content)
            results = []
            total_score = 0
            used_keywords = set()  # 跟踪已使用的关键词

            for i, sentence in enumerate(sentences, 1):
                best_match = None
                best_score = 0

                for keyword_data in keywords_data:
                    # 检查关键词是否已使用过
                    if keyword_data['original'] in used_keywords:
                        continue
                        
                    match_score = self.match_keywords(sentence, keyword_data)
                    if match_score > best_score:
                        best_score = match_score
                        best_match = keyword_data

                hit_keyword_group = best_match['original'] if best_match else ""
                match_strategy = KEYWORD_CONFIG['sequence_match_strategy'] if best_match and best_match['strategy'] else KEYWORD_CONFIG['full_match_strategy']

                # 如果匹配到关键词，将其加入已使用集合
                if best_match and best_score > 0:
                    used_keywords.add(best_match['original'])

                results.append({
                    'sentence_order': i,
                    'sentence_text': sentence,
                    'hit_keyword_group': hit_keyword_group,
                    'match_strategy': match_strategy,
                    'score': best_score
                })

                total_score += best_score

            # 应用最高分限制
            max_score = KEYWORD_CONFIG['max_score_limit']
            if total_score > max_score:
                total_score = max_score

            return results, total_score, content

        except Exception as e:
            print(f"分析文件 {file_path} 失败: {e}")
            return [], 0, ""

    def save_single_result(self, result_dir, filename, results):
        """保存单个录音文件的分析结果到Excel"""
        try:
            wb = Workbook()
            ws = wb.active
            ws.title = "分析结果"

            headers = ['录音文件名', '句子顺序', '句子文本', '命中关键词组', '命中策略', '得分']
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            for row, result in enumerate(results, 2):
                ws.cell(row=row, column=1, value=filename)
                ws.cell(row=row, column=2, value=result['sentence_order'])
                ws.cell(row=row, column=3, value=result['sentence_text'])
                ws.cell(row=row, column=4, value=result['hit_keyword_group'])
                ws.cell(row=row, column=5, value=result['match_strategy'])
                ws.cell(row=row, column=6, value=result['score'])

            result_file = os.path.join(result_dir, f"{os.path.splitext(filename)[0]}_分析结果.xlsx")
            wb.save(result_file)
            print(f"保存单个结果文件: {result_file}")

        except Exception as e:
            print(f"保存单个结果失败: {e}")

    def save_summary_result(self, result_dir, summary_data):
        """保存汇总结果到Excel"""
        try:
            wb = Workbook()
            ws = wb.active
            ws.title = "汇总结果"

            headers = ['录音文件名', '人员姓名', '所属队伍', '录音开始时间', '录音结束时间', '总得分']
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            for row, (filename, data) in enumerate(summary_data.items(), 2):
                ws.cell(row=row, column=1, value=filename)
                ws.cell(row=row, column=2, value=data['username'])
                ws.cell(row=row, column=3, value=data['group_name'])
                ws.cell(row=row, column=4, value=data['luyin_start_time'])
                ws.cell(row=row, column=5, value=data['luyin_end_time'])
                ws.cell(row=row, column=6, value=data['total_score'])

            summary_file = os.path.join(result_dir, "汇总结果.xlsx")
            wb.save(summary_file)
            print(f"保存汇总结果文件: {summary_file}")

        except Exception as e:
            print(f"保存汇总结果失败: {e}")

    def save_to_database(self, filename, content, results, total_score, username, group_name,
                         luyin_start_time, luyin_end_time, luyin_time):
        """保存数据到MySQL数据库 - 支持重复检查"""
        conn = None
        try:
            conn = pymysql.connect(**self.db_config)
            cursor = conn.cursor()

            # 生成文件名的MD5哈希值
            username_md5 = hashlib.md5(filename.encode('utf-8')).hexdigest()
            
            # 检查记录是否已存在
            cursor.execute("SELECT id FROM audio_file WHERE username_md5 = %s", (username_md5,))
            existing_record = cursor.fetchone()
            
            if existing_record:
                print(f"数据库记录已存在，跳过: {filename} (MD5: {username_md5[:8]}...)")
                return False

            # 开始事务
            conn.begin()

            # 插入录音文件记录
            cursor.execute("""
                INSERT INTO audio_file (file_name, original_text, total_score, 
                                      group_name, username, luyin_start_time, luyin_end_time, 
                                      post, luyin_time, username_md5)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (filename, content, total_score, group_name, username,
                  luyin_start_time, luyin_end_time, self.subject, luyin_time, username_md5))

            audio_file_id = cursor.lastrowid

            # 批量插入句子分析记录
            if results:
                sentence_data = [
                    (
                        audio_file_id,
                        result['sentence_order'],
                        result['sentence_text'],
                        result['hit_keyword_group'],
                        result['match_strategy'],
                        result['score']
                    )
                    for result in results
                ]

                # 使用executemany进行批量插入
                cursor.executemany("""
                    INSERT INTO audio_sentence (audio_file_id, sentence_order, sentence_text, 
                                              hit_keyword_group, match_strategy, score)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, sentence_data)

            # 提交事务
            conn.commit()
            print(f"数据库保存成功: {filename} (科目: {self.subject}, 录音时间: {luyin_time}) - 共保存 {len(results)} 条句子记录 (MD5: {username_md5[:8]}...)")
            return True

        except Exception as e:
            if conn:
                conn.rollback()
            print(f"数据库保存失败 {filename}: {e}")
            return False
        finally:
            if conn:
                cursor.close()
                conn.close()

    def process_kemu3_directory(self, dir_info):
        """处理单个科目三目录"""
        print(f"\n=== 处理科目三目录 {dir_info['luyin_time']} ===")
        
        # 查找并加载关键词文件
        keyword_file = self.find_keyword_file()
        if not keyword_file:
            print(f"跳过目录 {dir_info['path']}: 未找到科目三关键词文件")
            return

        keywords_data = self.load_keywords(keyword_file)
        if not keywords_data:
            print(f"跳过目录 {dir_info['path']}: 关键词加载失败")
            return

        # 创建result目录
        result_dir = os.path.join(dir_info['path'], FILE_CONFIG['result_dir_name'])
        if not os.path.exists(result_dir):
            os.makedirs(result_dir)

        # 获取录音文件
        audio_files = []
        for f in os.listdir(dir_info['path']):
            if f.endswith(tuple(FILE_CONFIG['supported_extensions'])):
                audio_files.append(f)

        if not audio_files:
            print(f"未找到录音文件: {dir_info['path']}")
            return

        print(f"找到 {len(audio_files)} 个录音文件")
        summary_data = {}
        processed_count = 0
        skipped_count = 0

        # 处理每个录音文件
        for filename in audio_files:
            print(f"处理文件: {filename}")
            file_path = os.path.join(dir_info['path'], filename)

            username, group_name, luyin_start_time, luyin_end_time = self.parse_filename(filename)
            results, total_score, content = self.analyze_audio_file(file_path, keywords_data)

            if results:
                # 保存Excel结果
                self.save_single_result(result_dir, filename, results)

                # 保存到数据库（会自动检查重复）
                if self.save_to_database(filename, content, results, total_score,
                                       username, group_name, luyin_start_time, luyin_end_time,
                                       dir_info['luyin_time']):
                    processed_count += 1
                else:
                    skipped_count += 1

                # 记录汇总数据
                summary_data[filename] = {
                    'username': username,
                    'group_name': group_name,
                    'luyin_start_time': luyin_start_time,
                    'luyin_end_time': luyin_end_time,
                    'total_score': total_score
                }

                print(f"完成: {filename}, 得分: {total_score}")
            else:
                print(f"处理失败: {filename}")

        # 同名人员去重：保留同名人员中得分最高的记录
        if summary_data:
            # 按人员姓名分组
            user_groups = {}
            for filename, data in summary_data.items():
                username = data['username']
                if username not in user_groups:
                    user_groups[username] = []
                user_groups[username].append((filename, data))
            
            # 保留每个人员得分最高的记录
            filtered_summary = {}
            removed_files = []
            
            for username, files in user_groups.items():
                if len(files) > 1:
                    # 按总得分排序，保留最高分的
                    files.sort(key=lambda x: x[1]['total_score'], reverse=True)
                    best_file = files[0]
                    filtered_summary[best_file[0]] = best_file[1]
                    
                    # 记录被移除的文件
                    for removed_file in files[1:]:
                        removed_files.append(removed_file[0])
                        print(f"移除低分记录: {removed_file[0]} (得分: {removed_file[1]['total_score']}) - 保留 {best_file[0]} (得分: {best_file[1]['total_score']})")
                else:
                    # 只有一个文件，直接保留
                    filtered_summary[files[0][0]] = files[0][1]
            
            # 保存过滤后的汇总结果
            self.save_summary_result(result_dir, filtered_summary)
            
            if removed_files:
                print(f"同名去重完成，移除了 {len(removed_files)} 个低分文件")

        print(f"科目三目录处理完成！新增: {processed_count}, 跳过: {skipped_count}")

    def process_all_kemu3(self):
        """处理所有科目三目录"""
        scan_results = self.scan_kemu3_directories()
        
        if not scan_results:
            print("未找到科目三目录")
            return

        print(f"\n找到 {len(scan_results)} 个科目三目录需要处理")

        for dir_info in scan_results:
            self.process_kemu3_directory(dir_info)

        print(f"\n=== 科目三批量处理完成 ===")
        print(f"处理的目录数: {len(scan_results)} 个")


def main():
    """主函数"""
    print("=== 录音文本分析系统 - 科目三专用版本 ===")
    print("扫描录音目录下的所有科目三目录")
    print("支持重复检查，避免重复上传")
    print()

    analyzer = AudioTextAnalyzer()
    analyzer.process_all_kemu3()

    print("\n程序执行完成！")
    input("按回车键退出...")


if __name__ == "__main__":
    main() 