#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能上下文分析器 - 解决跨句子关键词匹配问题
功能：
1. 动态上下文窗口
2. 语义连贯性检测
3. 教学场景识别
4. 多层次匹配策略
"""

import re
from typing import List, Dict, Tuple, Optional
from datetime import datetime, timedelta
from collections import defaultdict

# 简化版中文分词 - 不依赖jieba
def simple_chinese_tokenize(text: str) -> List[str]:
    """简化版中文分词"""
    # 基于标点符号和空格分词
    words = []
    current_word = ""

    for char in text:
        if char.isspace() or char in '，。！？；：、':
            if current_word:
                words.append(current_word)
                current_word = ""
        else:
            current_word += char

    if current_word:
        words.append(current_word)

    # 进一步分割长词
    final_words = []
    for word in words:
        if len(word) > 4:
            # 尝试按常见词汇分割
            for i in range(2, len(word)):
                if word[:i] in ['方向盘', '后视镜', '离合器', '刹车', '油门', '倒车', '入库', '观察', '控制']:
                    final_words.append(word[:i])
                    if word[i:]:
                        final_words.append(word[i:])
                    break
            else:
                final_words.append(word)
        else:
            final_words.append(word)

    return [w for w in final_words if len(w) >= 1]


class IntelligentContextAnalyzer:
    def __init__(self):
        # 教学场景模板
        self.teaching_scenarios = {
            '倒车入库': {
                'keywords': ['倒车', '入库', '后视镜', '方向盘', '车库', '车身', '边线'],
                'sequence': ['挂倒档', '观察后视镜', '控制车速', '修正方向', '停车到位'],
                'duration_range': (60, 600),  # 1-10分钟
                'key_phrases': ['倒车入库', '看后视镜', '打方向', '修正', '停车']
            },
            '侧方停车': {
                'keywords': ['侧方', '停车', '转向灯', '倒车', '车位', '边线'],
                'sequence': ['打转向灯', '观察后方', '倒车入位', '回正方向'],
                'duration_range': (30, 300),
                'key_phrases': ['侧方停车', '打灯', '倒车', '入位']
            },
            '直角转弯': {
                'keywords': ['直角', '转弯', '内轮差', '车速', '转向'],
                'sequence': ['减速慢行', '观察内轮差', '适时转向', '保持车距'],
                'duration_range': (20, 120),
                'key_phrases': ['直角转弯', '内轮差', '转向', '车速']
            },
            '曲线行驶': {
                'keywords': ['曲线', 'S弯', '车道', '方向盘', '轨迹'],
                'sequence': ['控制车速', '观察车道', '微调方向', '保持轨迹'],
                'duration_range': (30, 180),
                'key_phrases': ['曲线行驶', 'S弯', '车道', '轨迹']
            },
            '坡道定点': {
                'keywords': ['坡道', '定点', '停车', '起步', '手刹', '半联动'],
                'sequence': ['上坡定点', '拉手刹', '半联动起步', '松手刹'],
                'duration_range': (60, 300),
                'key_phrases': ['坡道定点', '半联动', '手刹', '起步']
            }
        }
        
        # 教学质量评估关键词
        self.quality_indicators = {
            '安全意识': ['观察', '注意', '安全', '危险', '避让', '检查'],
            '操作规范': ['标准', '规范', '正确', '错误', '改正', '调整'],
            '技能掌握': ['熟练', '掌握', '练习', '重复', '巩固', '提高'],
            '理论讲解': ['原理', '规则', '标准', '要求', '评判', '扣分'],
            '耐心指导': ['慢慢来', '不要急', '再试试', '很好', '继续', '加油']
        }

    def detect_teaching_scenario(self, sentences: List[str], 
                               time_stamps: List[Optional[datetime]] = None) -> Dict:
        """检测教学场景"""
        # 合并所有句子文本
        full_text = ' '.join(sentences)
        
        scenario_scores = {}
        for scenario_name, scenario_info in self.teaching_scenarios.items():
            score = 0.0
            
            # 关键词匹配得分
            keyword_matches = 0
            for keyword in scenario_info['keywords']:
                if keyword in full_text:
                    keyword_matches += 1
            
            if scenario_info['keywords']:
                keyword_score = keyword_matches / len(scenario_info['keywords'])
                score += keyword_score * 0.4
            
            # 关键短语匹配得分
            phrase_matches = 0
            for phrase in scenario_info['key_phrases']:
                if phrase in full_text:
                    phrase_matches += 1
            
            if scenario_info['key_phrases']:
                phrase_score = phrase_matches / len(scenario_info['key_phrases'])
                score += phrase_score * 0.6
            
            scenario_scores[scenario_name] = score
        
        # 找到最可能的场景
        best_scenario = max(scenario_scores, key=scenario_scores.get)
        best_score = scenario_scores[best_scenario]
        
        return {
            'detected_scenario': best_scenario if best_score > 0.3 else '通用教学',
            'confidence': best_score,
            'all_scores': scenario_scores,
            'scenario_info': self.teaching_scenarios.get(best_scenario, {})
        }

    def create_semantic_groups(self, sentences: List[str]) -> List[Dict]:
        """创建语义相关的句子组"""
        if not sentences:
            return []
        
        groups = []
        current_group = {
            'sentences': [sentences[0]],
            'indices': [0],
            'keywords': set(),
            'scenario': None
        }
        
        # 提取第一个句子的关键词
        first_keywords = set(simple_chinese_tokenize(sentences[0]))
        current_group['keywords'] = first_keywords

        for i in range(1, len(sentences)):
            sentence = sentences[i]
            sentence_keywords = set(simple_chinese_tokenize(sentence))
            
            # 计算与当前组的语义相似度
            similarity = self.calculate_group_similarity(
                sentence_keywords, current_group['keywords']
            )
            
            # 如果相似度高或组太小，加入当前组
            if similarity > 0.3 or len(current_group['sentences']) < 2:
                current_group['sentences'].append(sentence)
                current_group['indices'].append(i)
                current_group['keywords'].update(sentence_keywords)
            else:
                # 开始新组
                groups.append(current_group)
                current_group = {
                    'sentences': [sentence],
                    'indices': [i],
                    'keywords': sentence_keywords,
                    'scenario': None
                }
        
        # 添加最后一组
        if current_group['sentences']:
            groups.append(current_group)
        
        # 为每组检测教学场景
        for group in groups:
            scenario_info = self.detect_teaching_scenario(group['sentences'])
            group['scenario'] = scenario_info['detected_scenario']
            group['scenario_confidence'] = scenario_info['confidence']
        
        return groups

    def calculate_group_similarity(self, new_keywords: set, group_keywords: set) -> float:
        """计算新句子与当前组的语义相似度"""
        if not group_keywords:
            return 0.0
        
        # 交集比例
        intersection = new_keywords & group_keywords
        union = new_keywords | group_keywords
        
        if not union:
            return 0.0
        
        jaccard_similarity = len(intersection) / len(union)
        
        # 考虑教学专业词汇的权重
        professional_words = {'方向盘', '离合', '刹车', '油门', '档位', '后视镜', 
                            '倒车', '入库', '观察', '控制', '修正'}
        
        professional_intersection = intersection & professional_words
        if professional_intersection:
            jaccard_similarity += len(professional_intersection) * 0.2
        
        return min(jaccard_similarity, 1.0)

    def enhanced_context_matching(self, sentences: List[str], 
                                parsed_keywords: List[List[str]], 
                                base_score: int) -> Tuple[float, Dict]:
        """增强的上下文匹配"""
        # 创建语义组
        semantic_groups = self.create_semantic_groups(sentences)
        
        match_results = {
            'semantic_groups': len(semantic_groups),
            'group_matches': [],
            'total_confidence': 0.0,
            'best_group_score': 0.0
        }
        
        best_score = 0.0
        total_group_confidence = 0.0
        
        for group_idx, group in enumerate(semantic_groups):
            # 合并组内所有句子
            group_text = ' '.join(group['sentences'])
            
            # 在组内进行关键词匹配
            group_score = self.match_keywords_in_group(
                group_text, parsed_keywords, base_score, group['scenario']
            )
            
            group_match_info = {
                'group_index': group_idx,
                'sentences_count': len(group['sentences']),
                'scenario': group['scenario'],
                'scenario_confidence': group['scenario_confidence'],
                'match_score': group_score,
                'sentences': group['sentences'][:2]  # 只保存前两个句子用于调试
            }
            
            match_results['group_matches'].append(group_match_info)
            
            # 更新最佳得分
            if group_score > best_score:
                best_score = group_score
            
            # 累计置信度
            total_group_confidence += group['scenario_confidence']
        
        # 计算最终得分
        if semantic_groups:
            avg_scenario_confidence = total_group_confidence / len(semantic_groups)
            # 结合最佳匹配得分和场景识别置信度
            final_score = best_score * (0.8 + avg_scenario_confidence * 0.2)
        else:
            final_score = 0.0
        
        match_results['total_confidence'] = total_group_confidence
        match_results['best_group_score'] = best_score
        match_results['final_score'] = final_score
        
        return final_score, match_results

    def match_keywords_in_group(self, group_text: str, parsed_keywords: List[List[str]], 
                              base_score: int, scenario: str) -> float:
        """在语义组内进行关键词匹配"""
        matched_groups = 0
        total_confidence = 0.0
        
        for keyword_group in parsed_keywords:
            group_best_score = 0.0
            
            for keyword in keyword_group:
                # 精确匹配
                if keyword in group_text:
                    group_best_score = max(group_best_score, 1.0)
                    continue
                
                # 模糊匹配
                words = simple_chinese_tokenize(group_text)
                for word in words:
                    if len(word) >= 2:
                        # 编辑距离相似度
                        similarity = self.calculate_edit_distance_similarity(keyword, word)
                        if similarity > 0.8:
                            group_best_score = max(group_best_score, similarity * 0.9)
            
            if group_best_score > 0.6:
                matched_groups += 1
                total_confidence += group_best_score
        
        # 计算匹配得分
        if matched_groups == len(parsed_keywords):
            # 全部匹配
            avg_confidence = total_confidence / len(parsed_keywords)
            score = base_score * avg_confidence
            
            # 场景加权
            if scenario in self.teaching_scenarios:
                score *= 1.1  # 识别到具体教学场景，加权10%
            
            return score
        elif matched_groups >= len(parsed_keywords) * 0.8:
            # 部分匹配
            avg_confidence = total_confidence / len(parsed_keywords)
            return base_score * avg_confidence * 0.8
        else:
            return 0.0

    def calculate_edit_distance_similarity(self, s1: str, s2: str) -> float:
        """计算编辑距离相似度"""
        if not s1 or not s2:
            return 0.0
        
        # 动态规划计算编辑距离
        m, n = len(s1), len(s2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        
        for i in range(m + 1):
            dp[i][0] = i
        for j in range(n + 1):
            dp[0][j] = j
        
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if s1[i-1] == s2[j-1]:
                    dp[i][j] = dp[i-1][j-1]
                else:
                    dp[i][j] = min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1]) + 1
        
        edit_distance = dp[m][n]
        max_len = max(m, n)
        
        return 1.0 - (edit_distance / max_len) if max_len > 0 else 0.0
