#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
与原系统直接对比测试
对比原版luyin_kemu2.py的匹配逻辑与优化版本的效果
"""

import re
import difflib
from typing import List, Dict, Tuple


class OriginalMatcher:
    """原版本的匹配逻辑（从luyin_kemu2.py提取）"""
    
    def parse_keywords(self, keywords_str):
        """解析关键词字符串"""
        if not keywords_str:
            return []
        
        # 按 | 分割关键词组
        groups = keywords_str.split('|')
        parsed = []
        
        for group in groups:
            # 按 + 分割子关键词
            sub_keywords = [kw.strip() for kw in group.split('+') if kw.strip()]
            if sub_keywords:
                parsed.append(sub_keywords)
        
        return parsed
    
    def full_match(self, sentence, parsed_keywords, score):
        """全匹配：句子需包含所有子关键词"""
        for sub_keywords in parsed_keywords:
            found = False
            for keyword in sub_keywords:
                if keyword in sentence:
                    found = True
                    break
            if not found:
                return 0
        return score
    
    def sequence_match(self, sentence, parsed_keywords, score):
        """顺序匹配：按关键词组顺序依次出现"""
        last_pos = -1
        
        for sub_keywords in parsed_keywords:
            min_pos = len(sentence)
            found = False
            
            for keyword in sub_keywords:
                pos = sentence.find(keyword, last_pos + 1)
                if pos != -1:
                    min_pos = min(min_pos, pos)
                    found = True
            
            if not found or min_pos <= last_pos:
                return 0
            
            last_pos = min_pos
        
        return score
    
    def match_keywords(self, sentence, keywords_str, strategy=False, score=10):
        """匹配关键词并计算得分"""
        parsed_keywords = self.parse_keywords(keywords_str)
        
        if not strategy:
            return self.full_match(sentence, parsed_keywords, score)
        else:
            return self.sequence_match(sentence, parsed_keywords, score)


class OptimizedMatcher:
    """优化版本的匹配逻辑"""
    
    def __init__(self):
        # 同义词词典
        self.synonyms = {
            '方向盘': ['方向', '转向盘', '舵'],
            '离合器': ['离合', '离合踏板'],
            '刹车': ['制动', '刹车踏板', '制动踏板'],
            '油门': ['加速踏板', '油门踏板'],
            '后视镜': ['反光镜', '倒车镜'],
            '倒车': ['后退', '倒退', '退车'],
            '入库': ['进库', '停车入位'],
            '观察': ['看', '注意', '观看', '查看'],
            '控制': ['掌控', '把控', '调节'],
            '车速': ['速度', '车的速度'],
            '修正': ['调整', '纠正', '改正']
        }
        
        # 语音识别错误映射
        self.speech_errors = {
            '方向般': '方向盘', '方向盘盘': '方向盘',
            '离和': '离合', '离和器': '离合器',
            '刹者': '刹车', '刹茶': '刹车',
            '有门': '油门', '后事镜': '后视镜', '后是镜': '后视镜',
            '到车': '倒车', '倒者': '倒车',
            '入裤': '入库', '入苦': '入库',
            '观查': '观察', '观茶': '观察',
            '车数': '车速', '车宿': '车速',
            '修整': '修正', '调正': '调整'
        }
    
    def preprocess_text(self, text: str) -> str:
        """文本预处理"""
        # 移除时间戳和说话人标识
        text = re.sub(r'\[.*?\]', '', text)
        text = re.sub(r'^[^：]+：', '', text).strip()
        
        # 语音识别错误纠正
        for error, correct in self.speech_errors.items():
            text = text.replace(error, correct)
        
        # 移除语气词
        filler_words = ['嗯', '啊', '呃', '那个', '这个', '就是说', '然后']
        for filler in filler_words:
            text = text.replace(filler, ' ')
        
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    def enhanced_keyword_match(self, keyword: str, text: str) -> float:
        """增强的关键词匹配"""
        # 1. 精确匹配
        if keyword in text:
            return 1.0
        
        # 2. 同义词匹配
        if keyword in self.synonyms:
            for synonym in self.synonyms[keyword]:
                if synonym in text:
                    return 0.9
        
        # 3. 模糊匹配
        words = text.split()
        best_score = 0.0
        
        for word in words:
            if len(word) >= 2:
                similarity = difflib.SequenceMatcher(None, keyword, word).ratio()
                if similarity > 0.75:
                    best_score = max(best_score, similarity * 0.8)
        
        return best_score
    
    def parse_keywords(self, keywords_str):
        """解析关键词字符串"""
        if not keywords_str:
            return []
        
        groups = keywords_str.split('|')
        parsed = []
        
        for group in groups:
            sub_keywords = [kw.strip() for kw in group.split('+') if kw.strip()]
            if sub_keywords:
                parsed.append(sub_keywords)
        
        return parsed
    
    def enhanced_full_match(self, sentence, parsed_keywords, score):
        """增强的全匹配"""
        processed_sentence = self.preprocess_text(sentence)
        
        for sub_keywords in parsed_keywords:
            found = False
            best_score = 0.0
            
            for keyword in sub_keywords:
                match_score = self.enhanced_keyword_match(keyword, processed_sentence)
                if match_score > 0.6:  # 阈值
                    found = True
                    best_score = max(best_score, match_score)
            
            if not found:
                return 0
        
        return int(score * (best_score if best_score > 0 else 1.0))
    
    def match_keywords(self, sentence, keywords_str, strategy=False, score=10):
        """匹配关键词并计算得分"""
        parsed_keywords = self.parse_keywords(keywords_str)
        
        # 目前只实现全匹配的优化版本
        return self.enhanced_full_match(sentence, parsed_keywords, score)


def run_comparison_test():
    """运行对比测试"""
    print("=" * 70)
    print("原系统 vs 优化系统 直接对比测试")
    print("=" * 70)
    
    original = OriginalMatcher()
    optimized = OptimizedMatcher()
    
    # 测试用例 - 模拟真实的关键词配置和录音文本
    test_cases = [
        {
            'name': '语音识别错误测试',
            'sentence': '张旭 [00:05:12] 现在你要观查后事镜，慢慢到车入裤，注意控制方向般。',
            'keywords': '观察+后视镜|倒车+入库|控制+方向盘',
            'strategy': False,
            'score': 10,
            'description': '包含多个语音识别错误'
        },
        {
            'name': '同义词表达测试',
            'sentence': '张旭 [00:10:15] 你看一下反光镜，把车往后退，转向盘要慢慢打。',
            'keywords': '观察+后视镜|倒车|方向盘',
            'strategy': False,
            'score': 15,
            'description': '使用同义词表达'
        },
        {
            'name': '顺序匹配测试',
            'sentence': '张旭 [00:15:20] 先观察后视镜，然后倒车，最后入库。',
            'keywords': '观察+后视镜|倒车|入库',
            'strategy': True,  # 顺序匹配
            'score': 20,
            'description': '需要按顺序匹配的场景'
        },
        {
            'name': '复杂场景测试',
            'sentence': '张旭 [00:20:25] 嗯，那个，现在练习倒车入库，首先观查后事镜，然后慢慢到车，车数要控制好，修整方向般。',
            'keywords': '倒车+入库|观察+后视镜|控制+车速|修正+方向盘',
            'strategy': False,
            'score': 25,
            'description': '包含多种问题的综合场景'
        },
        {
            'name': '部分匹配测试',
            'sentence': '张旭 [00:25:30] 注意观察，控制好速度。',
            'keywords': '观察+后视镜|控制+车速|倒车+入库',
            'strategy': False,
            'score': 12,
            'description': '只能部分匹配的场景'
        }
    ]
    
    total_original_score = 0
    total_optimized_score = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {test_case['name']}")
        print("-" * 60)
        print(f"场景描述: {test_case['description']}")
        print(f"录音文本: {test_case['sentence']}")
        print(f"关键词配置: {test_case['keywords']}")
        print(f"匹配策略: {'顺序匹配' if test_case['strategy'] else '全匹配'}")
        print(f"基础分值: {test_case['score']}")
        
        # 原版本匹配
        original_score = original.match_keywords(
            test_case['sentence'], 
            test_case['keywords'], 
            test_case['strategy'], 
            test_case['score']
        )
        
        # 优化版本匹配
        optimized_score = optimized.match_keywords(
            test_case['sentence'], 
            test_case['keywords'], 
            test_case['strategy'], 
            test_case['score']
        )
        
        # 显示预处理效果
        processed_text = optimized.preprocess_text(test_case['sentence'])
        print(f"预处理后: {processed_text}")
        
        print(f"\n匹配结果:")
        print(f"  原版本得分: {original_score}")
        print(f"  优化版得分: {optimized_score}")
        print(f"  得分提升: {optimized_score - original_score:+d}")
        
        if original_score == 0 and optimized_score > 0:
            print(f"  ✅ 优化版本成功匹配了原版本无法匹配的内容")
        elif optimized_score > original_score:
            print(f"  📈 优化版本得分更高")
        elif optimized_score == original_score and original_score > 0:
            print(f"  ✓ 两版本得分相同，都成功匹配")
        elif original_score > 0 and optimized_score == 0:
            print(f"  ⚠️ 优化版本需要调整阈值")
        else:
            print(f"  ❌ 两版本都未匹配成功")
        
        total_original_score += original_score
        total_optimized_score += optimized_score
    
    # 总结
    print(f"\n" + "=" * 70)
    print("对比测试总结")
    print("=" * 70)
    print(f"测试用例数量: {len(test_cases)}")
    print(f"原版本总得分: {total_original_score}")
    print(f"优化版总得分: {total_optimized_score}")
    print(f"得分提升: {total_optimized_score - total_original_score:+d}")
    
    if total_optimized_score > total_original_score:
        improvement_rate = ((total_optimized_score - total_original_score) / max(total_original_score, 1)) * 100
        print(f"提升率: {improvement_rate:.1f}%")
        print(f"✅ 优化版本显著优于原版本")
    elif total_optimized_score == total_original_score:
        print(f"✓ 两版本得分相同，优化版本增加了容错能力")
    else:
        print(f"⚠️ 需要进一步调优参数")
    
    print(f"\n核心改进点:")
    print(f"🔧 语音识别错误自动纠正")
    print(f"🔧 同义词智能识别")
    print(f"🔧 模糊匹配算法")
    print(f"🔧 智能文本预处理")
    
    print(f"\n建议:")
    print(f"1. 可以将优化逻辑集成到现有的luyin_kemu2.py中")
    print(f"2. 保持原有的数据库和Excel配置不变")
    print(f"3. 只需要替换match_keywords相关方法")
    print("=" * 70)


if __name__ == "__main__":
    run_comparison_test()
