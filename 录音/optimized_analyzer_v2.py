#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版录音文本分析器 V2.0 - 集成多种优化策略
目标：将匹配准确率从70%提升到90%+

主要优化：
1. 集成增强型关键词匹配器
2. 智能上下文分析
3. 多策略融合
4. 质量评估机制
"""

import os
import re
import pandas as pd
import pymysql
from openpyxl import Workbook
from openpyxl.styles import PatternFill, Font, Alignment
from datetime import datetime
import hashlib
from typing import List, Dict, Tuple, Optional
import json

# 导入自定义优化模块
from enhanced_keyword_matcher import EnhancedKeywordMatcher
from intelligent_context_analyzer import IntelligentContextAnalyzer
from config import DB_CONFIG, FILE_CONFIG, KEYWORD_CONFIG, TEAM_CONFIG


class OptimizedAudioAnalyzerV2:
    def __init__(self, subject: str = '科目二'):
        # 基础配置
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        self.project_root = os.path.dirname(self.current_dir)
        self.subject = subject
        self.db_config = DB_CONFIG
        self.team_members = TEAM_CONFIG
        
        # 初始化优化组件
        self.enhanced_matcher = EnhancedKeywordMatcher()
        self.context_analyzer = IntelligentContextAnalyzer()
        
        # 性能统计
        self.performance_stats = {
            'total_files': 0,
            'successful_matches': 0,
            'accuracy_scores': [],
            'strategy_usage': defaultdict(int),
            'processing_times': []
        }
        
        print(f"=== 优化版录音分析器 V2.0 ===")
        print(f"当前科目: {self.subject}")
        print(f"录音目录: {self.current_dir}")
        print(f"工程根目录: {self.project_root}")

    def find_keyword_file(self):
        """查找关键词文件"""
        for filename in os.listdir(self.project_root):
            if filename.startswith(self.subject) and filename.endswith('.xlsx'):
                keyword_file = os.path.join(self.project_root, filename)
                print(f"找到{self.subject}关键词文件: {filename}")
                return keyword_file
        
        print(f"错误: 未找到 {self.subject} 的关键词文件")
        return None

    def load_keywords(self, keyword_file):
        """加载关键词数据"""
        try:
            df = pd.read_excel(keyword_file, header=0)
            keywords_data = []
            
            for index, row in df.iterrows():
                if pd.isna(row.iloc[1]):
                    continue

                keyword_group = str(row.iloc[1]).strip()
                strategy = str(row.iloc[2]).strip() if not pd.isna(row.iloc[2]) else ""
                score = int(row.iloc[3]) if not pd.isna(row.iloc[3]) else 0

                parsed_keywords = self.parse_keyword_group(keyword_group)

                keywords_data.append({
                    'original': keyword_group,
                    'parsed': parsed_keywords,
                    'strategy': strategy,
                    'score': score
                })

            print(f"成功加载 {len(keywords_data)} 个关键词组")
            return keywords_data

        except Exception as e:
            print(f"加载关键词文件失败: {e}")
            return []

    def parse_keyword_group(self, keyword_group):
        """解析关键词组"""
        sub_keywords = [kw.strip() for kw in keyword_group.split(',')]
        parsed = []
        
        for sub_kw in sub_keywords:
            if '|' in sub_kw:
                or_keywords = [k.strip() for k in sub_kw.split('|')]
                parsed.append(or_keywords)
            else:
                parsed.append([sub_kw.strip()])

        return parsed

    def enhanced_sentence_splitting(self, text_content: str) -> List[str]:
        """增强的句子分割"""
        lines = text_content.strip().split('\n')
        sentences = []
        i = 0

        while i < len(lines):
            current_line = lines[i].strip()

            # 跳过客户对话
            if current_line.startswith('客户'):
                i += 1
                continue

            # 处理教练对话
            if re.search(r'\[.*?\]', current_line):
                if i + 1 < len(lines):
                    next_line = lines[i + 1].strip()
                    # 检查下一行是否也是客户对话
                    if not next_line.startswith('客户') and next_line:
                        sentence = current_line + " " + next_line
                        sentences.append(sentence.strip())
                    else:
                        sentences.append(current_line)
                    i += 2
                else:
                    sentences.append(current_line)
                    i += 1
            else:
                i += 1

        return sentences

    def multi_strategy_analysis(self, sentences: List[str], keywords_data: List[Dict]) -> Tuple[List[Dict], float]:
        """多策略分析：结合多种匹配方法"""
        results = []
        total_score = 0
        used_keywords = set()
        
        # 策略1：逐句分析（原有方法的增强版）
        sentence_results = self.analyze_sentences_individually(sentences, keywords_data, used_keywords)
        
        # 策略2：上下文窗口分析
        context_results = self.analyze_with_context_windows(sentences, keywords_data, used_keywords)
        
        # 策略3：语义组分析
        semantic_results = self.analyze_with_semantic_groups(sentences, keywords_data, used_keywords)
        
        # 融合多种策略的结果
        final_results = self.merge_analysis_results(
            sentence_results, context_results, semantic_results
        )
        
        # 计算总分
        for result in final_results:
            total_score += result['score']
        
        # 应用最高分限制
        max_score = KEYWORD_CONFIG['max_score_limit']
        if total_score > max_score:
            total_score = max_score
        
        return final_results, total_score

    def analyze_sentences_individually(self, sentences: List[str], keywords_data: List[Dict], 
                                     used_keywords: set) -> List[Dict]:
        """逐句分析（增强版）"""
        results = []
        
        for i, sentence in enumerate(sentences):
            best_match = None
            best_score = 0.0
            best_details = {}
            
            for keyword_data in keywords_data:
                if keyword_data['original'] in used_keywords:
                    continue
                
                # 使用增强匹配器
                score, details = self.enhanced_matcher.multi_strategy_match(
                    sentence, keyword_data['parsed'], keyword_data['strategy'], keyword_data['score']
                )
                
                if score > best_score:
                    best_score = score
                    best_match = keyword_data
                    best_details = details
            
            # 记录结果
            if best_match and best_score > 0:
                used_keywords.add(best_match['original'])
                
            results.append({
                'sentence_order': i + 1,
                'sentence_text': sentence,
                'hit_keyword_group': best_match['original'] if best_match else "",
                'match_strategy': best_details.get('primary_strategy', 'no_match'),
                'score': best_score,
                'confidence': best_details.get('confidence', 0.0),
                'analysis_method': 'individual_sentence',
                'match_details': best_details
            })
        
        return results

    def analyze_with_context_windows(self, sentences: List[str], keywords_data: List[Dict], 
                                   used_keywords: set) -> List[Dict]:
        """上下文窗口分析"""
        results = []
        window_size = 3
        
        for i, sentence in enumerate(sentences):
            # 获取智能上下文窗口
            target_keywords = []
            for kw_data in keywords_data:
                if kw_data['original'] not in used_keywords:
                    for group in kw_data['parsed']:
                        target_keywords.extend(group)
            
            context_info = self.enhanced_matcher.intelligent_context_window(
                sentences, i, target_keywords
            )
            
            if len(context_info['window_sentences']) > 1:
                # 在上下文中进行匹配
                context_text = ' '.join(context_info['window_sentences'])
                
                best_match = None
                best_score = 0.0
                
                for keyword_data in keywords_data:
                    if keyword_data['original'] in used_keywords:
                        continue
                    
                    score, details = self.enhanced_matcher.multi_strategy_match(
                        context_text, keyword_data['parsed'], keyword_data['strategy'], keyword_data['score']
                    )
                    
                    if score > best_score:
                        best_score = score
                        best_match = keyword_data
                
                if best_match and best_score > 0:
                    results.append({
                        'sentence_order': i + 1,
                        'sentence_text': sentence,
                        'hit_keyword_group': best_match['original'],
                        'match_strategy': 'context_window',
                        'score': best_score * 0.9,  # 上下文匹配略微降权
                        'confidence': context_info['total_relevance'],
                        'analysis_method': 'context_window',
                        'context_info': context_info
                    })
        
        return results

    def analyze_with_semantic_groups(self, sentences: List[str], keywords_data: List[Dict], 
                                   used_keywords: set) -> List[Dict]:
        """语义组分析"""
        results = []
        
        # 创建语义组
        semantic_groups = self.context_analyzer.create_semantic_groups(sentences)
        
        for group in semantic_groups:
            group_text = ' '.join(group['sentences'])
            
            best_match = None
            best_score = 0.0
            
            for keyword_data in keywords_data:
                if keyword_data['original'] in used_keywords:
                    continue
                
                score = self.context_analyzer.match_keywords_in_group(
                    group_text, keyword_data['parsed'], keyword_data['score'], group['scenario']
                )
                
                if score > best_score:
                    best_score = score
                    best_match = keyword_data
            
            if best_match and best_score > 0:
                # 为组内每个句子创建结果记录
                for idx in group['indices']:
                    results.append({
                        'sentence_order': idx + 1,
                        'sentence_text': sentences[idx],
                        'hit_keyword_group': best_match['original'],
                        'match_strategy': 'semantic_group',
                        'score': best_score / len(group['indices']),  # 平分到每个句子
                        'confidence': group['scenario_confidence'],
                        'analysis_method': 'semantic_group',
                        'group_info': {
                            'scenario': group['scenario'],
                            'group_size': len(group['sentences'])
                        }
                    })

        return results

    def merge_analysis_results(self, sentence_results: List[Dict],
                             context_results: List[Dict],
                             semantic_results: List[Dict]) -> List[Dict]:
        """融合多种分析策略的结果"""
        # 按句子顺序组织结果
        merged_results = {}

        # 处理逐句分析结果
        for result in sentence_results:
            order = result['sentence_order']
            if order not in merged_results or result['score'] > merged_results[order]['score']:
                merged_results[order] = result

        # 处理上下文分析结果
        for result in context_results:
            order = result['sentence_order']
            if order not in merged_results or result['score'] > merged_results[order]['score']:
                merged_results[order] = result

        # 处理语义组分析结果
        for result in semantic_results:
            order = result['sentence_order']
            if order not in merged_results or result['score'] > merged_results[order]['score']:
                merged_results[order] = result

        # 转换为列表并排序
        final_results = []
        for order in sorted(merged_results.keys()):
            result = merged_results[order]
            if result['score'] > 0:  # 只保留有得分的结果
                final_results.append(result)

        return final_results
